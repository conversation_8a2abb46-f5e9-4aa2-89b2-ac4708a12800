{"name": "bagisto/bagisto", "description": "Bagisto Laravel E-Commerce", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "require": {"php": "^8.1", "ext-soap": "*", "astrotomic/laravel-translatable": "^11.0.0", "bacon/bacon-qr-code": "^2.0", "bagisto/bagisto-package-generator": "dev-master", "bagisto/legacy-api": "^1.0", "bagisto/rest-api": "dev-master", "bagistobrasil/bagisto-product-social-share": "^0.1.2", "barryvdh/laravel-debugbar": "^3.1", "barryvdh/laravel-dompdf": "^2.0.0", "chillerlan/php-qrcode": "^5.0", "diglactic/laravel-breadcrumbs": "^7.0", "doctrine/dbal": "^2.9", "enshrined/svg-sanitize": "^0.15.0", "fakerphp/faker": "^1.14", "flynsarmy/db-blade-compiler": "dev-master", "guzzlehttp/guzzle": "^7.0.1", "guzzlehttp/oauth-subscriber": "^0.6", "intervention/image": "^2.4", "intervention/imagecache": "^2.5.2", "iyzico/iyzipay-php": "^2.0", "kalnoy/nestedset": "^6.0", "khaled.alshamaa/ar-php": "^6.0.0", "konekt/concord": "^1.2", "laravel/framework": "^9.0", "laravel/horizon": "^5.17", "laravel/sanctum": "^2.12", "laravel/scout": "^9.0", "laravel/socialite": "^5.0", "laravel/telescope": "^5.8", "laravel/tinker": "^2.0", "laravel/ui": "^3.0", "league/flysystem-aws-s3-v3": "^3.29", "maatwebsite/excel": "^3.1.26", "onesignal/onesignal-php-api": "*@dev", "opcodesio/log-viewer": "^3.4", "paypal/paypal-checkout-sdk": "1.0.1", "pragmarx/google2fa-laravel": "^2.1", "predis/predis": "^2.2", "prettus/l5-repository": "^2.6", "pusher/pusher-php-server": "^7.0", "spatie/laravel-ignition": "^1.0", "spatie/laravel-sitemap": "^6.1", "thorne/launchpad": "dev-main", "thorne/services-test": "dev-main", "thornebilisim/fast-order": "dev-main", "thornebilisim/security-layer": "0.1.0", "thornebilisim/sell-gold": "dev-main", "thornebilisim/sumsub-kyc": "dev-main", "thornebilisim/wallet": "dev-main"}, "require-dev": {"brianium/paratest": "^6.11", "codeception/codeception": "*@dev", "codeception/module-asserts": "^3.0.0", "codeception/module-filesystem": "^3.0.0", "codeception/module-laravel": "*@dev", "codeception/module-rest": "^3.0.0", "codeception/module-webdriver": "^3.0.0", "filp/whoops": "^2.0", "laravel/pint": "^1.22", "mockery/mockery": "^1.3.1", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5"}, "replace": {"bagisto/laravel-user": "v0.1.0", "bagisto/laravel-admin": "v0.1.0", "bagisto/laravel-ui": "v0.1.0", "bagisto/laravel-core": "v0.1.0", "bagisto/laravel-attribute": "v0.1.0", "bagisto/laravel-checkout": "v0.1.0", "bagisto/laravel-customer": "v0.1.0", "bagisto/laravel-inventory": "v0.1.0", "bagisto/laravel-category": "v0.1.0", "bagisto/laravel-product": "v0.1.0", "bagisto/laravel-shop": "v0.1.0", "bagisto/laravel-theme": "v0.1.0", "bagisto/laravel-shipping": "v0.1.0", "bagisto/laravel-payment": "v0.1.0", "bagisto/laravel-sales": "v0.1.0", "bagisto/laravel-tax": "v0.1.0", "bagisto/laravel-api": "v0.1.0", "bagisto/laravel-paypal": "v0.1.0", "bagisto/laravel-discount": "v0.1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Webkul\\User\\": "packages/Webkul/User/src", "Webkul\\Admin\\": "packages/Webkul/Admin/src", "Webkul\\Ui\\": "packages/Webkul/Ui/src", "Webkul\\Category\\": "packages/Webkul/Category/src", "Webkul\\Checkout\\": "packages/Webkul/Checkout/src", "Webkul\\Attribute\\": "packages/Webkul/Attribute/src", "Webkul\\Shop\\": "packages/Webkul/Shop/src", "Webkul\\Core\\": "packages/Webkul/Core/src", "Webkul\\Customer\\": "packages/Webkul/Customer/src", "Webkul\\Inventory\\": "packages/Webkul/Inventory/src", "Webkul\\Product\\": "packages/Webkul/Product/src", "Webkul\\Theme\\": "packages/Webkul/Theme/src", "Webkul\\Shipping\\": "packages/Webkul/Shipping/src", "Webkul\\Payment\\": "packages/Webkul/Payment/src", "Webkul\\Paypal\\": "packages/Webkul/Paypal/src", "Webkul\\Sales\\": "packages/Webkul/Sales/src", "Webkul\\Tax\\": "packages/Webkul/Tax/src", "Webkul\\CatalogRule\\": "packages/Webkul/CatalogRule/src", "Webkul\\CartRule\\": "packages/Webkul/CartRule/src", "Webkul\\Rule\\": "packages/Webkul/Rule/src", "Webkul\\CMS\\": "packages/Webkul/CMS/src", "Webkul\\Velocity\\": "packages/Webkul/Velocity/src", "Webkul\\BookingProduct\\": "packages/Webkul/BookingProduct/src", "Webkul\\SocialLogin\\": "packages/Webkul/SocialLogin/src", "Webkul\\DebugBar\\": "packages/Webkul/DebugBar/src", "Webkul\\Marketing\\": "packages/Webkul/Marketing/src", "Webkul\\Notification\\": "packages/Webkul/Notification/src", "Webkul\\Sitemap\\": "packages/Webkul/Sitemap/src", "Webkul\\IyzicoPayment\\": "packages/Webkul/IyzicoPayment/src", "PayWithCrypto\\": "packages/PayWithCrypto/src"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\API\\": "tests/api/", "Tests\\Functional\\": "tests/functional/", "Tests\\Trigger\\": "tests/trigger/", "Tests\\Unit\\": "tests/unit/", "tests\\": "tests/", "Thorne\\FastOrder\\Tests\\": "packages/thornebilisim/fast-order/tests/", "Tests\\Feature\\Thorne\\FastOrder\\": "packages/thornebilisim/fast-order/tests/Feature/"}}, "extra": {"laravel": {"dont-discover": ["laravel/dusk"]}}, "repositories": [{"type": "path", "url": "packages/thorne/*", "options": {"symlink": true}}, {"type": "path", "url": "packages/thornebilisim/*", "options": {"symlink": true}}], "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate", "Webkul\\Core\\Events\\ComposerEvents::postCreateProject"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover"], "test": ["set -e", "@php artisan migrate:fresh --env=testing", "vendor/bin/codecept run unit", "vendor/bin/codecept run functional", "vendor/bin/codecept run trigger", "vendor/bin/codecept run api"], "test-win": ["@set -e", "@php artisan migrate:fresh --env=testing", "vendor\\bin\\codecept.bat run unit", "vendor\\bin\\codecept.bat run functional", "vendor\\bin\\codecept.bat run trigger", "vendor\\bin\\codecept.bat run api"]}, "config": {"preferred-install": "stable", "sort-packages": true, "optimize-autoloader": true}, "minimum-stability": "dev", "prefer-stable": true, "support": {"issues": "https://github.com/bagisto/bagisto/issues", "forum": "https://forums.bagisto.com", "source": "https://github.com/bagisto/bagisto", "docs": "https://devdocs.bagisto.com"}}