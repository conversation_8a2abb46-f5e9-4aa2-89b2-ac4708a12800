@extends('shop::customers.account.index')

@section('page_title')
    {{ __('shop::app.customer.account.address.index.page-title') }}
@endsection

@section('page-detail-wrapper')
    @if ($addresses->isEmpty())
        <a href="{{ route('customer.address.create') }}" class="bg-terra-soft-khaki-green px-4 py-2 rounded-xl hover:opacity-90 mt-3 unset float-right">
            <div class="cursor-pointer font-terramirum text-white font-bold text-md text-truncate text-center">{{ __('shop::app.customer.account.address.index.add') }}</div>
        </a>
    @endif

    <div class="account-head mt-3 ml-5">
        <span class="account-heading">
            {{ __('velocity::app-static.user-profile.address') }}
        </span>

        @if (! $addresses->isEmpty())
            <span class="account-action">
                <a href="{{ route('customer.address.create') }}" class="bg-terra-soft-khaki-green px-4 py-2 rounded-xl hover:opacity-90 mt-3 unset float-right">
                    <div class="cursor-pointer font-terramirum text-white font-bold text-md text-truncate text-center">{{ __('shop::app.customer.account.address.index.add') }}</div>
                </a>
            </span>
        @endif
    </div>

    {!! view_render_event('bagisto.shop.customers.account.address.list.before', ['addresses' => $addresses]) !!}

        <div class="account-table-content ml-5">
            @if ($addresses->isEmpty())
                    <div class="empty">
                        <div class="pt-10 pb-14 bg-off-white rounded-50p mt-5 ml-3 flex flex-col justify-center items-center space-y-7">
                            <div class="p-5 bg-terra-khaki-green rounded-2xl">
                                <img src="/deneme/svg/green-star.svg" alt="">
                            </div>
                            <div class="font-terramirum font-bold text-2xl text-center">{{ __('velocity::app-static.address.empty-msg') }}</div>
                            <div class="font-terramirum font-bold text-lg text-center text-terra-via-gray">{{ __('velocity::app-static.address.empty-content') }}</div>
                        </div>
                    </div>
                    @else
                <div class="row address-holder no-padding">
                    @foreach ($addresses as $address)
                    <div class="col-12 col-sm-6 col-lg-4">
                        <div class="card mx-auto border-1p border-terra-orange rounded-10p p-0 peer-checked:bg-off-white">
                            {{-- <div class="card m-1"> --}}
                                <div class="flex space-x-2 card-title mt-3">
                                    <img class="ml-3" src="/deneme/svg/account-green.svg" alt="">
                                    <label for="addresses.id"><div class="text-2xl cursor-pointer leading-1 text-black">{{ $address->first_name }} {{ $address->last_name }}</div></label>
                                </div>

                                <div class="card-body">
                                    {{-- <h5 class="card-title fw6">{{ $address->first_name }} {{ $address->last_name }}</h5> --}}

                                    <ul type="none">
                                        <li>{{ $address->address1 }}</li>
                                        <li>{{ $address->city }}</li>
                                        <li>{{ $address->state }}</li>
                                        <li>{{ core()->country_name($address->country) }} {{ $address->postcode }}</li>
                                        <li>
                                            {{ __('shop::app.customer.account.address.index.contact') }} : {{ $address->phone }}
                                        </li>
                                    </ul>
                                    
                                    <div class="row justify-center mt-2">
                                        <a class="card-link" href="{{ route('customer.address.edit', $address->id) }}">
                                            <div class=" justify-center items-center font-terramirum font-bold bg-terra-orange hover:opacity-90 text-white rounded-2xl py-2 px-10 self-center text-sm tracking"> {{ __('shop::app.customer.account.address.index.edit') }} </div>
                                         </a>
     
                                         <a class="card-link" href="javascript:void(0);" onclick="deleteAddress('{{ __('shop::app.customer.account.address.index.confirm-delete') }}', '{{ $address->id }}')">
                                             <div class="justify-center items-center font-terramirum font-bold bg-terra-orange hover:opacity-90 text-white rounded-2xl py-2 px-10 self-center text-sm tracking"> {{ __('shop::app.customer.account.address.index.delete') }} </div>
                                         </a>
     
                                    </div>

                                    <form id="deleteAddressForm{{ $address->id }}" action="{{ route('address.delete', $address->id) }}" method="post">
                                        @method('delete')

                                        @csrf
                                    </form>
                                {{-- </div> --}}
                            </div>
                        </div>

                    </div>
                    @endforeach
                </div>
            @endif
        </div>

    {!! view_render_event('bagisto.shop.customers.account.address.list.after', ['addresses' => $addresses]) !!}
@endsection

@push('scripts')
    <script>
        function deleteAddress(message, addressId) {
            if (! confirm(message)) {
                return;
            }

            $(`#deleteAddressForm${addressId}`).submit();
        }
    </script>
@endpush

@if ($addresses->isEmpty())
    <style>
        a#add-address-button {
            position: absolute;
            margin-top: 92px;
        }

        .address-button {
            position: absolute;
            z-index: 1 !important;
            margin-top: 110px !important;
        }
    </style>
@endif
