<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class CryptoPaymentAdminNotification extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(public $order) {}

    public function build()
    {
        $sender = core()->getSenderEmailDetails();
        $status = $this->order?->status ?? 'failed';

        return $this->from(data_get($sender, 'email'), data_get($sender, 'name'))
            ->to(data_get($sender, 'email'), data_get($sender, 'name'))
            ->subject(trans('mail.sales.order.admin-payment-notification.subject', ['order_id' => $this->order->increment_id]))
            ->view('shop::emails.sales.admin-payment-notification');
    }
}
