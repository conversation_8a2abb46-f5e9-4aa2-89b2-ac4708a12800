<?php

return [
    [
        'key'  => 'sales.wallet-deposit',
        'name' => 'wallet-deposit::app.admin.system.name',
        'sort' => 5,
    ],

    [
        'key'    => 'sales.wallet-deposit.settings',
        'name'   => 'wallet-deposit::app.admin.system.settings',
        'sort'   => 0,
        'fields' => [
            [
                'name'          => 'reference_code_prefix',
                'title'         => 'wallet-deposit::app.admin.system.fields.reference_code_prefix',
                'type'          => 'text',
                'validation'    => 'required',
                'default_value' => config('wallet-deposit.reference_code_prefix'),
                'channel_based' => false,
                'locale_based'  => false,
            ],
            [
                'name'          => 'status',
                'title'         => 'wallet-deposit::app.admin.system.fields.status',
                'type'          => 'boolean',
                'validation'    => 'required',
                'default_value' => config('wallet-deposit.status'),
                'channel_based' => false,
                'locale_based'  => false,
            ],
        ],
    ],

    [
        'key'    => 'sales.wallet-deposit.cash',
        'name'   => 'wallet-deposit::app.admin.system.cash',
        'sort'   => 1,
        'fields' => [
            [
                'name'           => 'title',
                'title'          => 'wallet-deposit::app.admin.system.fields.title',
                'type'           => 'text',
                'validation'     => 'required',
                'default_value'  => config('wallet-deposit.sources.cash.title'),
                'channel_based'  => false,
                'locale_based'   => true,
            ],
            [
                'name'           => 'placeholder',
                'title'          => 'wallet-deposit::app.admin.system.fields.placeholder',
                'type'           => 'textarea',
                'validation'     => 'required',
                'default_value'  => config('wallet-deposit.sources.cash.placeholder'),
                'channel_based'  => false,
                'locale_based'   => true,
            ],
            [
                'name'           => 'status',
                'title'          => 'wallet-deposit::app.admin.system.fields.status',
                'type'           => 'boolean',
                'validation'     => 'required',
                'default_value'  => config('wallet-deposit.sources.cash.status'),
                'channel_based'  => false,
                'locale_based'   => false,
            ],
            [
                'name'           => 'can_deposit',
                'title'          => 'wallet-deposit::app.admin.system.fields.can_deposit',
                'type'           => 'boolean',
                'validation'     => 'required',
                'default_value'  => config('wallet-deposit.sources.cash.can_deposit'),
                'channel_based'  => false,
                'locale_based'   => false,
            ],
            [
                'name'           => 'can_withdraw',
                'title'          => 'wallet-deposit::app.admin.system.fields.can_withdraw',
                'type'           => 'boolean',
                'validation'     => 'required',
                'default_value'  => config('wallet-deposit.sources.cash.can_withdraw'),
                'channel_based'  => false,
                'locale_based'   => false,
            ],
        ],
    ],

    [
        'key'    => 'sales.wallet-deposit.money-transfer',
        'name'   => 'wallet-deposit::app.admin.system.money-transfer',
        'sort'   => 2,
        'fields' => [
            [
                'name'           => 'title',
                'title'          => 'wallet-deposit::app.admin.system.fields.title',
                'type'           => 'text',
                'validation'     => 'required',
                'default_value'  => config('wallet-deposit.sources.money-transfer.title'),
                'channel_based'  => false,
                'locale_based'   => true,
            ],
            [
                'name'           => 'placeholder',
                'title'          => 'wallet-deposit::app.admin.system.fields.placeholder',
                'type'           => 'textarea',
                'validation'     => 'required',
                'default_value'  => config('wallet-deposit.sources.money-transfer.placeholder'),
                'channel_based'  => false,
                'locale_based'   => true,
            ],
            [
                'name'           => 'status',
                'title'          => 'wallet-deposit::app.admin.system.fields.status',
                'type'           => 'boolean',
                'validation'     => 'required',
                'default_value'  => config('wallet-deposit.sources.money-transfer.status'),
                'channel_based'  => false,
                'locale_based'   => false,
            ],
            [
                'name'           => 'can_deposit',
                'title'          => 'wallet-deposit::app.admin.system.fields.can_deposit',
                'type'           => 'boolean',
                'validation'     => 'required',
                'default_value'  => config('wallet-deposit.sources.money-transfer.can_deposit'),
                'channel_based'  => false,
                'locale_based'   => false,
            ],
            [
                'name'           => 'can_withdraw',
                'title'          => 'wallet-deposit::app.admin.system.fields.can_withdraw',
                'type'           => 'boolean',
                'validation'     => 'required',
                'default_value'  => config('wallet-deposit.sources.money-transfer.can_withdraw'),
                'channel_based'  => false,
                'locale_based'   => false,
            ],
        ],
    ],
];
