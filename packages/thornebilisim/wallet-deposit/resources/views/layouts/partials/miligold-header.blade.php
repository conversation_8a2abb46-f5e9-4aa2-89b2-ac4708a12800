<header class="js-page-header fixed top-0 z-[50] w-full backdrop-blur transition-colors bg-black/20">
    <div class="flex items-center px-6 pb-2 pt-4 xl:px-24">
        <!-- Logo -->
        <a href="/" class="shrink-0 p-1 rounded-full">
            <picture>
                {{--                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/logo-and-milyem.webp" type="image/webp">--}}
                <source srcset="/miligold/img/miligold-logo.png" type="image/png">
                <img src="/miligold/img/miligold-logo.png" alt="miliGOLD" class="max-h-12 md:max-h-20">
            </picture>
        </a>
        <div class="js-mobile-menu invisible lg:visible fixed inset-0 z-10 ml-auto items-center bg-white opacity-0 dark:bg-jacarta-800 lg:relative lg:inset-auto lg:flex lg:bg-transparent lg:opacity-100 dark:lg:bg-transparent">
            <!-- Mobile Logo / Menu Close -->
            <div class="t-0 fixed left-0 z-10 flex w-full items-center justify-between bg-white p-6 dark:bg-jacarta-800 lg:hidden">
                <!-- Mobile Logo -->
                <a href="/" class="shrink-0">
                    <picture>
                        {{--                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/logo-and-milyem.webp" type="image/webp">--}}
                        <source srcset="/miligold/img/miligold-logo.png" type="image/png">
                        <img src="/miligold/img/miligold-logo.png" alt="miliGOLD" class="max-h-20">
                    </picture>
                </a>
                <!-- Mobile Menu Close -->
                <button
                    class="js-mobile-close group ml-2 flex h-10 w-10 items-center justify-center rounded-full border border-jacarta-100 bg-white transition-colors hover:border-transparent hover:bg-[#caa754] focus:border-transparent focus:bg-[#caa754] dark:border-transparent dark:bg-white/[.15] dark:hover:bg-[#caa754]"
                    aria-label="close mobile menu">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"
                         class="h-4 w-4 fill-jacarta-700 transition-colors group-hover:fill-white group-focus:fill-white dark:fill-white">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path
                            d="M12 10.586l4.95-4.95 1.414 1.414-4.95 4.95 4.95 4.95-1.414 1.414-4.95-4.95-4.95 4.95-1.414-1.414 4.95-4.95-4.95-4.95L7.05 5.636z"></path>
                    </svg>
                </button>
            </div>
            <!-- Primary Nav -->
            <nav class="navbar w-full">
                <ul class="flex flex-col lg:flex-row">
                    <li class="nav-item group relative">
                        <a href="/"
                           class="dropdown-toggle flex items-center justify-between py-3.5 font-display text-base text-jacarta-700 hover:text-[#caa754] focus:text-[#caa754] dark:text-white dark:hover:text-[#caa754] dark:focus:text-[#caa754] lg:px-5"
                           id="navDropdown-1">
                            {!! __('velocity::app-gold.footer.home') !!}
                        </a>
                    </li>
                    <li class="js-nav-dropdown group relative">
                        <a href="#"
                           class="dropdown-toggle flex items-center justify-between py-3.5 font-display text-base text-jacarta-700 hover:text-[#caa754] focus:text-[#caa754] dark:text-white dark:hover:text-[#caa754] dark:focus:text-[#caa754] lg:px-5"
                           id="navDropdown-4" aria-expanded="false" role="button" data-bs-toggle="dropdown">
                            {!! __('velocity::app-gold.İnstitutional') !!} <i class="">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"
                                     class="h-4 w-4 dark:fill-white">
                                    <path fill="none" d="M0 0h24v24H0z"></path>
                                    <path d="M12 13.172l4.95-4.95 1.414 1.414L12 16 5.636 9.636 7.05 8.222z"></path>
                                </svg>
                            </i>
                        </a>
                        <ul class="dropdown-menu group-hover:visible lg:invisible left-0 top-[85%] z-10 hidden min-w-[200px] gap-x-4 whitespace-nowrap rounded-xl bg-white transition-all will-change-transform group-hover:opacity-100 dark:bg-jacarta-800 lg:absolute lg:grid lg:translate-y-4 lg:py-4 lg:px-2 lg:opacity-0 lg:shadow-2xl lg:group-hover:translate-y-2"
                            aria-labelledby="navDropdown-4">
                            <li>
                                <a href="{{route('shop.home.whats-milyem')}}"
                                   class="flex items-center rounded-xl px-5 py-2 transition-colors hover:bg-[#fddca3] hover:text-[#caa754] focus:text-[#caa754] dark:hover:bg-jacarta-600">
                                    <span class="font-display text-sm text-jacarta-700 dark:text-white">{!! __('velocity::app-gold.whats_milyem') !!}</span>
                                </a>

                            <li>
                                <a href="{{route('shop.home.about-us')}}"
                                   class="flex items-center rounded-xl px-5 py-2 transition-colors hover:bg-[#fddca3] hover:text-[#caa754] focus:text-[#caa754] dark:hover:bg-jacarta-600">
                                    <span class="font-display text-sm text-jacarta-700 dark:text-white">{!! __('velocity::app-gold.about_us') !!}</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{route('shop.home.our-vision')}}"
                                   class="flex items-center rounded-xl px-5 py-2 transition-colors hover:bg-[#fddca3] hover:text-[#caa754] focus:text-[#caa754] dark:hover:bg-jacarta-600">
                                    <span class="font-display text-sm text-jacarta-700 dark:text-white">{!! __('velocity::app-gold.vision') !!}</span>
                                </a>
                            </li>
                            {{--                    <li>--}}
                            {{--                        <a href="#"--}}
                            {{--                           class="flex items-center rounded-xl px-5 py-2 transition-colors hover:bg-jacarta-50 hover:text-[#caa754] focus:text-[#caa754] dark:hover:bg-jacarta-600">--}}
                            {{--                            <span class="font-display text-sm text-jacarta-700 dark:text-white">Kılavuz</span>--}}
                            {{--                        </a>--}}
                            {{--                    </li>--}}
                        </ul>
                    </li>
                    <li class="nav-item group relative">
                        <a href="{{route('shop.home.faq')}}"
                           class="dropdown-toggle flex items-center justify-between py-3.5 font-display text-base text-jacarta-700 hover:text-[#caa754] focus:text-[#caa754] dark:text-white dark:hover:text-[#caa754] dark:focus:text-[#caa754] lg:px-5">
                            {{ __('velocity::app-static.footer.faq') }}
                        </a>
                    </li>
                    <li class="nav-item group relative">
                        <a href="{{route('shop.home.contact-us')}}"
                           class=" flex items-center justify-between py-3.5 font-display text-base text-jacarta-700 hover:text-[#caa754] focus:text-[#caa754] dark:text-white dark:hover:text-[#caa754] dark:focus:text-[#caa754] lg:px-5">
                            {{ __('velocity::app-static.contact-us.heading') }}
                        </a>
                    </li>
                </ul>
            </nav>

            <!-- Mobile Connect Wallet / Socials -->
            <div class="mt-10 w-full lg:hidden">

                <!-- Socials -->
                <div class="flex items-center justify-center space-x-5">

                    <a href="#" class="group">
                        <svg aria-hidden="true" focusable="false" data-prefix="fab" data-icon="twitter"
                             class="h-5 w-5 fill-jacarta-300 group-hover:fill-[#caa754] dark:group-hover:fill-white"
                             role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                            <path
                                d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z"></path>
                        </svg>
                    </a>

                    <a href="#" class="group">
                        <svg aria-hidden="true" focusable="false" data-prefix="fab" data-icon="instagram"
                             class="h-5 w-5 fill-jacarta-300 group-hover:fill-[#caa754] dark:group-hover:fill-white"
                             role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
                            <path
                                d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z"></path>
                        </svg>
                    </a>

                </div>
            </div>

            <!-- Actions -->
            <div class="ml-4 flex">
                <div class="group relative">
                    <a href="/miligram"
                       class="whitespace-nowrap cursor-pointer font-display text-base text-jacarta-700 js-dark-mode-trigger group ml-2 flex h-10 px-8 items-center justify-center rounded-full border border-jacarta-100 transition-colors hover:border-transparent hover:bg-[#caa754] focus:border-transparent focus:bg-[#caa754] glowAnimation">
                        {{ __('velocity::app-static.moneytransfer.authorization.buy-button') }}
                    </a>
                </div>
                <div class=" group-dropdown relative">
                    <a href="{{ route('shop.checkout.onepage.index') }}"
                       class="group ml-2 flex h-10 w-10 items-center justify-center rounded-full border border-jacarta-100 bg-white transition-colors hover:border-transparent hover:bg-[#caa754] focus:border-transparent focus:bg-[#caa754] dark:border-transparent dark:bg-white/[.15] dark:hover:bg-[#caa754]"
                       id="cart">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="20" height="20">
                            <path d="M2.25 2.25a.75.75 0 0 0 0 1.5h1.386c.17 0 .318.114.362.278l2.558 9.592a3.752 3.752 0 0 0-2.806 3.63c0 .414.336.75.75.75h15.75a.75.75 0 0 0 0-1.5H5.378A2.25 2.25 0 0 1 7.5 15h11.218a.75.75 0 0 0 .674-.421 60.358 60.358 0 0 0 2.96-7.228.75.75 0 0 0-.525-.965A60.864 60.864 0 0 0 5.68 4.509l-.232-.867A1.875 1.875 0 0 0 3.636 2.25H2.25ZM3.75 20.25a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0ZM16.5 20.25a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Z" />
                        </svg>
                    </a>
                </div>
                <!-- Profile -->
                <div class=" group-dropdown relative">
                    <button
                        class="dropdown-toggle group ml-2 flex h-10 w-10 items-center justify-center rounded-full border border-jacarta-100 bg-white transition-colors hover:border-transparent hover:bg-[#caa754] focus:border-transparent focus:bg-[#caa754] dark:border-transparent dark:bg-white/[.15] dark:hover:bg-[#caa754]"
                        id="profileDropdown" aria-expanded="false" data-bs-toggle="dropdown" aria-label="profile">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"
                             class="h-4 w-4 fill-jacarta-700 transition-colors group-hover:fill-white group-focus:fill-white dark:fill-white">
                            <path fill="none" d="M0 0h24v24H0z"></path>
                            <path
                                d="M11 14.062V20h2v-5.938c3.946.492 7 3.858 7 7.938H4a8.001 8.001 0 0 1 7-7.938zM12 13c-3.315 0-6-2.685-6-6s2.685-6 6-6 6 2.685 6 6-2.685 6-6 6z"></path>
                        </svg>
                    </button>
                    @auth
                        <div
                            class="dropdown-menu group-dropdown-hover:visible lg:invisible !-right-4 !top-[85%] !left-auto z-10 hidden min-w-[14rem] whitespace-nowrap rounded-xl bg-white transition-all will-change-transform before:absolute before:-top-3 before:h-3 before:w-full group-dropdown-hover:opacity-100 dark:bg-jacarta-800 lg:absolute lg:grid lg:!translate-y-4 lg:py-4 lg:px-2 lg:opacity-0 lg:shadow-2xl"
                            aria-labelledby="profileDropdown">
                            <a href="/customer/account/profile"
                               class="flex items-center space-x-2 rounded-xl px-5 py-2 transition-colors hover:bg-[#fddca3] hover:text-[#caa754] focus:text-[#caa754] dark:hover:bg-jacarta-600">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"
                                     class="h-4 w-4 fill-jacarta-700 transition-colors dark:fill-white">
                                    <path fill="none" d="M0 0h24v24H0z"></path>
                                    <path
                                        d="M11 14.062V20h2v-5.938c3.946.492 7 3.858 7 7.938H4a8.001 8.001 0 0 1 7-7.938zM12 13c-3.315 0-6-2.685-6-6s2.685-6 6-6 6 2.685 6 6-2.685 6-6 6z"></path>
                                </svg>
                                <span class="mt-1 font-display text-sm text-jacarta-700 dark:text-white">{{ __('velocity::app.header.my-account') }}</span>
                            </a>
                            <a href="/customer/account/orders"
                               class="flex items-center space-x-2 rounded-xl px-5 py-2 transition-colors hover:bg-[#fddca3] hover:text-[#caa754] focus:text-[#caa754] dark:hover:bg-jacarta-600">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"
                                     class="h-4 w-4 fill-jacarta-700 transition-colors dark:fill-white">
                                    <path fill="none" d="M0 0h24v24H0z"></path>
                                    <path
                                        d="M9.954 2.21a9.99 9.99 0 0 1 4.091-.002A3.993 3.993 0 0 0 16 5.07a3.993 3.993 0 0 0 3.457.261A9.99 9.99 0 0 1 21.5 8.876 3.993 3.993 0 0 0 20 12c0 1.264.586 2.391 1.502 3.124a10.043 10.043 0 0 1-2.046 3.543 3.993 3.993 0 0 0-3.456.261 3.993 3.993 0 0 0-1.954 2.86 9.99 9.99 0 0 1-4.091.004A3.993 3.993 0 0 0 8 18.927a3.993 3.993 0 0 0-3.457-.26A9.99 9.99 0 0 1 2.5 15.121 3.993 3.993 0 0 0 4 11.999a3.993 3.993 0 0 0-1.502-3.124 10.043 10.043 0 0 1 2.046-3.543A3.993 3.993 0 0 0 8 5.071a3.993 3.993 0 0 0 1.954-2.86zM12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"></path>
                                </svg>
                                <span class="mt-1 font-display text-sm text-jacarta-700 dark:text-white">{{ __('velocity::app-static.user-profile.orders-tab') }}</span>
                            </a>
                            <a href="/customer/account/wallet"
                               class="flex items-center space-x-2 rounded-xl px-5 py-2 transition-colors hover:bg-[#fddca3] hover:text-[#caa754] focus:text-[#caa754] dark:hover:bg-jacarta-600">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"
                                     class="h-4 w-4 fill-jacarta-700 transition-colors dark:fill-white">
                                    <path fill="none" d="M0 0h24v24H0z"></path>
                                    <path
                                        d="M9.954 2.21a9.99 9.99 0 0 1 4.091-.002A3.993 3.993 0 0 0 16 5.07a3.993 3.993 0 0 0 3.457.261A9.99 9.99 0 0 1 21.5 8.876 3.993 3.993 0 0 0 20 12c0 1.264.586 2.391 1.502 3.124a10.043 10.043 0 0 1-2.046 3.543 3.993 3.993 0 0 0-3.456.261 3.993 3.993 0 0 0-1.954 2.86 9.99 9.99 0 0 1-4.091.004A3.993 3.993 0 0 0 8 18.927a3.993 3.993 0 0 0-3.457-.26A9.99 9.99 0 0 1 2.5 15.121 3.993 3.993 0 0 0 4 11.999a3.993 3.993 0 0 0-1.502-3.124 10.043 10.043 0 0 1 2.046-3.543A3.993 3.993 0 0 0 8 5.071a3.993 3.993 0 0 0 1.954-2.86zM12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"></path>
                                </svg>
                                <span class="mt-1 font-display text-sm text-jacarta-700 dark:text-white">{{ __('velocity::app-static.user-profile.wallet2') }}</span>
                            </a>
                            <form id="customerLogout" action="{{ route('customer.session.destroy') }}" method="POST">@csrf @method('DELETE') </form>
                            <a href="javascript:;" onclick="document.getElementById('customerLogout').submit();"
                               class="flex items-center space-x-2 rounded-xl px-5 py-2 transition-colors hover:bg-[#fddca3] hover:text-[#caa754] focus:text-[#caa754] dark:hover:bg-jacarta-600">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"
                                     class="h-4 w-4 fill-jacarta-700 transition-colors dark:fill-white">
                                    <path fill="none" d="M0 0h24v24H0z"></path>
                                    <path
                                        d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zM7 11V8l-5 4 5 4v-3h8v-2H7z"></path>
                                </svg>
                                <span class="mt-1 font-display text-sm text-jacarta-700 dark:text-white">{{ __('velocity::app-static.user-profile.logout') }}</span>
                            </a>
                        </div>
                    @endauth
                    @guest
                        <div
                            class="dropdown-menu group-dropdown-hover:visible lg:invisible !-right-4 !top-[85%] !left-auto z-10 hidden min-w-[14rem] whitespace-nowrap rounded-xl bg-white transition-all will-change-transform before:absolute before:-top-3 before:h-3 before:w-full group-dropdown-hover:opacity-100 dark:bg-jacarta-800 lg:absolute lg:grid lg:!translate-y-4 lg:py-4 lg:px-2 lg:opacity-0 lg:shadow-2xl"
                            aria-labelledby="profileDropdown">
                            <a href="{{ route('customer.session.create') }}"
                               class="flex items-center space-x-2 rounded-xl px-5 py-2 transition-colors hover:bg-[#fddca3] hover:text-[#caa754] focus:text-[#caa754] dark:hover:bg-jacarta-600">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"
                                     class="h-4 w-4 fill-jacarta-700 transition-colors dark:fill-white">
                                    <path fill="none" d="M0 0h24v24H0z"></path>
                                    <path
                                        d="M11 14.062V20h2v-5.938c3.946.492 7 3.858 7 7.938H4a8.001 8.001 0 0 1 7-7.938zM12 13c-3.315 0-6-2.685-6-6s2.685-6 6-6 6 2.685 6 6-2.685 6-6 6z"></path>
                                </svg>
                                <span class="mt-1 font-display text-sm text-jacarta-700 dark:text-white">{!! __('velocity::app-static.login.singin-button') !!}</span>
                            </a>
                            <a href="/customer/register"
                               class="flex items-center space-x-2 rounded-xl px-5 py-2 transition-colors hover:bg-[#fddca3] hover:text-[#caa754] focus:text-[#caa754] dark:hover:bg-jacarta-600">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"
                                     class="h-4 w-4 fill-jacarta-700 transition-colors dark:fill-white">
                                    <path fill="none" d="M0 0h24v24H0z"></path>
                                    <path
                                        d="M11 14.062V20h2v-5.938c3.946.492 7 3.858 7 7.938H4a8.001 8.001 0 0 1 7-7.938zM12 13c-3.315 0-6-2.685-6-6s2.685-6 6-6 6 2.685 6 6-2.685 6-6 6z"></path>
                                </svg>
                                <span class="mt-1 font-display text-sm text-jacarta-700 dark:text-white">{{ __('velocity::app-static.login.signup') }}</span>
                            </a>
                        </div>
                    @endguest
                </div>
                <div class=" group-dropdown relative">
                    <button
                        class="dropdown-toggle group ml-2 flex h-10 w-10 items-center justify-center rounded-full border border-jacarta-100 bg-white transition-colors hover:border-transparent hover:bg-[#caa754] focus:border-transparent focus:bg-[#caa754] dark:border-transparent dark:bg-white/[.15] dark:hover:bg-[#caa754]"
                        id="languageDropdown" aria-expanded="false" data-bs-toggle="dropdown" aria-label="language">
                        <svg fill="#000000" width="24" height="24" viewBox="0 0 512 512"
                             xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M363,176,246,464h47.24l24.49-58h90.54l24.49,58H480ZM336.31,362,363,279.85,389.69,362Z"></path>
                            <path
                                d="M272,320c-.25-.19-20.59-15.77-45.42-42.67,39.58-53.64,62-114.61,71.15-143.33H352V90H214V48H170V90H32v44H251.25c-9.52,26.95-27.05,69.5-53.79,108.36-32.68-43.44-47.14-75.88-47.33-76.22L143,152l-38,22,6.87,13.86c.89,1.56,17.19,37.9,54.71,86.57.92,1.21,1.85,2.39,2.78,3.57-49.72,56.86-89.15,79.09-89.66,79.47L64,368l23,36,19.3-11.47c2.2-1.67,41.33-24,92-80.78,24.52,26.28,43.22,40.83,44.3,41.67L255,362Z"></path>
                        </svg>
                    </button>
                    <div
                        class="dropdown-menu group-dropdown-hover:visible lg:invisible !-right-4 !top-[85%] !left-auto z-10 hidden min-w-[14rem] whitespace-nowrap rounded-xl bg-white transition-all will-change-transform before:absolute before:-top-3 before:h-3 before:w-full group-dropdown-hover:opacity-100 dark:bg-jacarta-800 lg:absolute lg:grid lg:!translate-y-4 lg:py-4 lg:px-2 lg:opacity-0 lg:shadow-2xl"
                        aria-labelledby="languageDropdown">
                        @foreach(app('Webkul\Core\Repositories\LocaleRepository')->all() as $locale)
                            <a href="{{ request()->fullUrlWithQuery(['locale' => $locale->code]) }}"
                               class="dropdown-item flex items-center justify-between rounded-xl px-5 py-2 font-display text-sm font-semibold transition-colors hover:bg-[#fddca3] dark:text-white dark:hover:bg-jacarta-600 text-jacarta-700">
                                <span>{{ $locale->name }}</span>
                            </a>
                        @endforeach
                    </div>
                </div>

            </div>
        </div>

        <!-- Mobile Menu Actions -->
        <div class="ml-auto flex lg:hidden">
            <!-- Profile -->
            <a href="#"
               class="group ml-2 flex h-10 w-10 items-center justify-center rounded-full border border-jacarta-100 bg-white transition-colors hover:border-transparent hover:bg-[#caa754] focus:border-transparent focus:bg-[#caa754] dark:border-transparent dark:bg-white/[.15] dark:hover:bg-[#caa754]"
               aria-label="profile">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"
                     class="h-4 w-4 fill-jacarta-700 transition-colors group-hover:fill-white group-focus:fill-white dark:fill-white">
                    <path fill="none" d="M0 0h24v24H0z"></path>
                    <path
                        d="M11 14.062V20h2v-5.938c3.946.492 7 3.858 7 7.938H4a8.001 8.001 0 0 1 7-7.938zM12 13c-3.315 0-6-2.685-6-6s2.685-6 6-6 6 2.685 6 6-2.685 6-6 6z"></path>
                </svg>
            </a>

            <!-- Mobile Menu Toggle -->
            <button
                class="js-mobile-toggle group ml-2 flex h-10 w-10 items-center justify-center rounded-full border border-jacarta-100 bg-white transition-colors hover:border-transparent hover:bg-[#caa754] focus:border-transparent focus:bg-[#caa754] dark:border-transparent dark:bg-white/[.15] dark:hover:bg-[#caa754]"
                aria-label="open mobile menu">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"
                     class="h-4 w-4 fill-jacarta-700 transition-colors group-hover:fill-white group-focus:fill-white dark:fill-white">
                    <path fill="none" d="M0 0h24v24H0z"></path>
                    <path d="M18 18v2H6v-2h12zm3-7v2H3v-2h18zm-3-7v2H6V4h12z"></path>
                </svg>
            </button>
        </div>
    </div>
</header>