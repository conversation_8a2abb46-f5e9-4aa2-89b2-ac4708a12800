{!! view_render_event('bagisto.shop.layout.header.account-item.before') !!}



<div id="account" class="relative lg:w-1/6 hidden lg:block">
    <div class=" welcome-content dropdown-toggle">
        {{-- <div class="flex items-center"> --}}

            @if (auth()->guard('customer')->user() && auth()->guard('customer')->user()->image)
            <img class="lg:max-h-[26px]" src="/deneme/svg/account.svg" alt="">
            @else
                <img class="lg:max-h-[26px]" src="/deneme/svg/account.svg" alt="">
            @endif

            {{-- @if (auth()->guard('customer')->user())
                <a class="unset" href="{{ route('customer.session.destroy') }}">
                    <img class="ml-5" src="/deneme/svg/logout-header.svg" alt="">
                </a>
            @endif --}}

        {{-- </div> --}}


    </div>

    @guest('customer')
        <div class="dropdown-list" style="right: 5px; width: 290px">
            <div class="modal-content dropdown-container">
                <div class="modal-header no-border pb0">
                    <label class="fs18 grey">{{ __('shop::app.header.title') }}</label>
                    {{-- <label class="fs18 grey">Hesap</label> --}}
                </div>

                {{-- <div class="fs14 content">
                    <p class="no-margin">{{ __('shop::app.header.dropdown-text') }}</p>
                </div> --}}


                 <div class="modal-footer justify-center">
                        <div class="bg-terra-soft-khaki-green px-4 py-2 rounded-full hover:opacity-90" style="box-shadow: 0 1px 2px rgb(50 123 5 /67%)">
                            <a class="unset" href="{{ route('customer.session.index') }}">
                                <div class="font-terramirum text-white font-bold text-sm text-truncate ">
                                    {{-- Giriş Yap --}}
                                    {{ __('velocity::app-static.login.page-title') }}
                                </div>
                            </a>
                        </div>
                        <div class="bg-terra-soft-khaki-green px-4 py-2 rounded-full hover:opacity-90" style="box-shadow: 0 1px 2px rgb(50 123 5 /67%)">
                            <a class="unset" href="{{ route('customer.register.index') }}">
                                <div class="font-terramirum text-white font-bold text-sm">
                                    {{-- Üye Ol --}}
                                    {{ __('velocity::app-static.signup.btn-text') }}

                                </div>
                            </a>
                        </div>

                    {{-- <a href="{{ route('customer.session.index') }}" class="theme-btn fs14 fw6">
                        {{ __('shop::app.header.sign-in') }}
                    </a>
                    <a href="{{ route('customer.register.index') }}" class="theme-btn fs14 fw6">
                        {{ __('shop::app.header.sign-up') }}
                    </a> --}}
                </div>
            </div>
        </div>
    @endguest
    @auth('customer')
        <div class="dropdown-list">
            <div class="text-xl px-3 py-2 text-terra-khaki-green border-terra-khaki-green font-bold text-capitalize" style="border-bottom-width: 2px!important;">
                {{ auth()->guard('customer')->user()->first_name }}
            </div>

            <div class="dropdown-container">
                <ul type="none">
                    {{-- <li>
                        <a href="{{ route('customer.profile.index') }}" class="unset">{{ __('shop::app.header.profile') }}</a>
                    </li>

                    <li>
                        <a href="{{ route('customer.orders.index') }}" class="unset">{{__('velocity::app-static.user-profile.wallet')}}</a>
                    </li>
                    <li>
                        <a href="{{ route('customer.advantages.index') }}" class="unset">{{__('velocity::app-static.user-profile.advantage')}}</a>
                    </li>
                    <li>
                        <a href="{{ route('customer.wishlist.index') }}" class="unset">{{__('velocity::app-static.user-profile.wishlist')}}</a>
                    </li>
                    <li>
                        <a href="{{ route('customer.address.index') }}" class="unset">{{__('velocity::app-static.user-profile.address')}}</a>
                    </li>
                    <li>
                        <a href="#" class="unset">{{__('velocity::app-static.user-profile.notification')}}</a>
                    </li> --}}

                    @if(!is_null(auth()->guard('customer')->user()->customer_verified_at))
                        <li>
                            <a href="{{ route('customer.profile.index') }}" class="unset">{{ __('shop::app.header.profile') }}</a>
                        </li>

                        <li>
                            <a href="{{ route('customer.orders.index') }}" class="unset">{{__('velocity::app-static.user-profile.wallet')}}</a>
                        </li>
                    @endif



{{--                    @php--}}
{{--                        $showCompare = core()->getConfigData('general.content.shop.compare_option') == "1" ? true : false;--}}

{{--                        $showWishlist = core()->getConfigData('general.content.shop.wishlist_option') == "1" ? true : false;--}}
{{--                    @endphp--}}

{{--                    @if ($showWishlist)--}}
{{--                        <li>--}}
{{--                            <a href="{{ route('customer.wishlist.index') }}" class="unset">{{ __('shop::app.header.wishlist') }}</a>--}}
{{--                        </li>--}}
{{--                    @endif--}}

{{--                    @if ($showCompare)--}}
{{--                        <li>--}}
{{--                            <a href="{{ route('velocity.customer.product.compare') }}" class="unset">{{ __('velocity::app.customer.compare.text') }}</a>--}}
{{--                        </li>--}}
{{--                    @endif--}}

                    <li>
                        <form id="customerLogout" action="{{ route('customer.session.destroy') }}" method="POST">
                            @csrf

                            @method('DELETE')
                        </form>

                        <a
                            class="unset"
                            style="color:rgb(245, 153, 61)!important;"
                            href="{{ route('customer.session.destroy') }}"
                            onclick="event.preventDefault(); document.getElementById('customerLogout').submit();">
                            <div class="flex space-x-2"><img src="/deneme/svg/logout.svg"class="w-1/12" alt="">
                                <span>{{ __('shop::app.header.logout') }}</span></div>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    @endauth
    @auth('customer')
        <div class="btn absolute right-0" style="bottom:-9px">
             <span class="badge special-badge" style="">
            {{\Illuminate\Support\Str::substr(auth()->guard('customer')->user()?->first_name, 0,1)}}
                 {{\Illuminate\Support\Str::substr(auth()->guard('customer')->user()?->last_name, 0,1)}}
        </span>
        </div>
    @endauth
</div>

{!! view_render_event('bagisto.shop.layout.header.account-item.after') !!}
