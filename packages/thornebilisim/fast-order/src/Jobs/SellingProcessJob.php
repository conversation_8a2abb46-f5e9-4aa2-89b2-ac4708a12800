<?php

namespace Thorne\FastOrder\Jobs;

use App\Services\OAuth\ApiClientAuth10Service;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Thorne\FastOrder\Jobs\Selling\SimulateCryptoPaymentJob;
use Throwable;
use Webkul\Customer\Models\Customer;
use Webkul\Sales\Models\Order;

class SellingProcessJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 5;

    public array $backoff = [3, 6, 9, 12, 15];

    public int $timeout = 60;

    private ApiClientAuth10Service $apiService;

    private Order $order;

    private Customer $customer;

    public function __construct(Order $order, ApiClientAuth10Service $apiService)
    {
        $this->order      = $order;
        $this->apiService = $apiService;
        $this->customer   = $order->customer;
    }

    public function handle()
    {
        dispatch(new SimulateCryptoPaymentJob($this->order, $this->apiService))->onQueue($this->order->getMetaByKey('queue_name')->meta_value ?? 'fast-order-low-sell');
    }

    public function failed(Throwable $exception)
    {
        Log::channel('fast-order')->critical('BuyingProcess:failed - all attempts exhausted', [
            'order_id'  => $this->order->id,
            'exception' => $exception->getMessage(),
            'attempts'  => $this->attempts(),
            'max_tries' => $this->tries,
        ]);

        $this->order->update(['status' => 'failed']);

        dispatch(new MaxRetryJob($this->order, 'payment'))->onQueue($this->order->getMetaByKey('queue_name')->meta_value ?? 'fast-order-low-sell');
    }
}
