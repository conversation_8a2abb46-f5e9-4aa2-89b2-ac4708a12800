
@if(request()->route()->getName() != 'customer.session.index' && request()->route()->getName() != 'customer.register.index')
    @if(!auth()->check())
            <section class="bulten max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:!max-w-7xl mx-auto py-9">
                <div class="font-terramirum text-xl text-center">
                    {{-- {{ __('velocity::app-static.subscriber.subscriber-text')}} --}}

                    <span class="font-bold">Thorne Bilişim A.Ş.</span> Öncelikle <span class="font-bold">“Model Dijital Ürün”</span> geliştirmek <br>
                    i<PERSON>in se<PERSON> <span class="font-bold">Gayrimenkul</span> sektöründe dijital çözüm önerileri geliştirmeyi
                    hedeflemektedir.
                </div>
                <div class="border-1 border-borderflat-gray2 px-10 py-8 rounded-60p mt-8 max-w-1/2 mx-auto" style="border:1px solid rgb(207, 207, 207);">
                    <div class="font-terramirum font-semibold text-3xl text-center">
                    {{-- {{ __('velocity::app-static.subscriber.subscribe')}} --}}

                        Bültenimize abone olun</div>
                    <form action="{{url('subscriber-form')}}" method="post" class="">
                        @csrf
                        <div class="self-center mt-46p relative group">
                            <input
                                name="fullname"
                                id="fullname"
                                type="text"
                                placeholder="Ad Soyad*"
                                autocomplete="off"
                                autofill="off"
                                class="peer placeholder-transparent font-bold text-lg !border-b-2 border-bordermid-gray w-full group-hover:placeholder-white focus:outline-none focus:ring-transparent focus:border-black"
                                required
                            />
                            <label for="fullname" class="font-terramirum font-regular absolute left-4 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-black text-xs peer-placeholder-shown:text-lg peer-placeholder-shown:top-0 peer-placeholder-shown:left-4 peer-focus:top-0 peer-focus:left-4 peer-focus:text-black peer-focus:text-xs">Ad Soyad*</label>
                        </div>
                        <div class="self-center mt-46p relative group">
                            <input
                                name="email"
                                id="email"
                                type="email"
                                placeholder="E-mail *"
                                autocomplete="off"
                                autofill="off"
                                class="peer placeholder-transparent font-bold text-lg !border-b-2 border-bordermid-gray w-full group-hover:placeholder-white focus:outline-none focus:ring-transparent focus:border-black"
                                required
                            />
                            <label for="email" class="font-terramirum font-regular absolute left-4 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-black text-xs peer-placeholder-shown:text-lg peer-placeholder-shown:top-0 peer-placeholder-shown:left-4 peer-focus:top-0 peer-focus:left-4 peer-focus:text-black peer-focus:text-xs">E-mail *</label>
                        </div>
                        <div class="self-center mt-27p relative group flex items-center">
                            <input name="kvkk" id="kvkk" class="cursor-pointer !w-6 !h-6 border-2 rounded-md focus:outline-none !checked:bg-black !focus:bg-black" type="checkbox" required />
                            <label for="kvkk" class="font-terramirum text-base text-center text-terra-dark-antrasit ml-6"><span>
                                {{-- {{ __('velocity::app-static.subscriber.kvkk')}} --}}
                                Gizlilik Beyanını</span> ve KVKK Politikasını kabul ediyorum</label>
                        </div>
                        <div class="mt-9 w-full flex justify-center">
                            <button class="bg-terra-light-soft-gray font-bold text-white rounded-full py-2 px-4 self-center font-light text-lg w-full max-w-324p" type="submit" style="box-shadow: 0 3px 6px rgb(0 0 0 / 16%)">Kaydol</button>
                        </div>

                    </form>
                </div>
            </section>

    @endif
@endif