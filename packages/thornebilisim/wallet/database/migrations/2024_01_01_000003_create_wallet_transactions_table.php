<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wallet_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id');
            $table->string('account_number', 11)->nullable(); // Related account number
            $table->string('reference', 100)->unique(); // Unique transaction reference
            $table->enum('type', ['deposit', 'withdrawal', 'transfer', 'fee', 'refund', 'adjustment']);
            $table->enum('direction', ['inbound', 'outbound']);
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled', 'expired']);
            $table->string('method', 50); // Payment method (bank_transfer, qonto, paypal, etc.)
            $table->string('currency', 10); // EUR, USD, MLGR
            $table->decimal('amount', 20, 8); // Transaction amount
            $table->decimal('fee', 20, 8)->default('0.********'); // Transaction fee
            $table->decimal('net_amount', 20, 8); // Amount after fees
            $table->decimal('exchange_rate', 20, 8)->nullable(); // Exchange rate if applicable
            $table->text('description')->nullable(); // Transaction description
            $table->json('metadata')->nullable(); // Additional transaction data
            $table->string('external_reference', 255)->nullable(); // External system reference
            $table->string('external_transaction_id', 255)->nullable(); // External transaction ID
            $table->timestamp('processed_at')->nullable(); // When transaction was processed
            $table->timestamp('failed_at')->nullable(); // When transaction failed
            $table->text('failure_reason')->nullable(); // Failure reason
            $table->unsignedBigInteger('parent_id')->nullable(); // Parent transaction for related transactions
            $table->timestamps();

            // Indexes
            $table->index(['customer_id']);
            $table->index(['account_number']);
            $table->index(['reference']);
            $table->index(['type']);
            $table->index(['status']);
            $table->index(['method']);
            $table->index(['currency']);
            $table->index(['direction']);
            $table->index(['created_at']);
            $table->index(['processed_at']);
            $table->index(['parent_id']);
            $table->index(['external_reference']);
            $table->index(['external_transaction_id']);

            // Composite indexes for common queries
            $table->index(['customer_id', 'currency', 'status']);
            $table->index(['customer_id', 'type', 'status']);
            $table->index(['method', 'status']);

            // Foreign key constraints
            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('cascade');
            $table->foreign('parent_id')->references('id')->on('wallet_transactions')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wallet_transactions');
    }
};
