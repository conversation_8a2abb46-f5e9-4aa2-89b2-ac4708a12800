<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wallet_transfers', function (Blueprint $table) {
            $table->id();
            $table->string('reference', 100)->unique(); // Unique transfer reference
            $table->unsignedBigInteger('from_customer_id'); // Sender customer
            $table->unsignedBigInteger('to_customer_id'); // Recipient customer
            $table->string('from_account_number', 11); // Sender account number
            $table->string('to_account_number', 11); // Recipient account number
            $table->string('currency', 10); // Transfer currency
            $table->decimal('amount', 20, 8); // Transfer amount
            $table->decimal('fee', 20, 8)->default('0.********'); // Transfer fee
            $table->decimal('net_amount', 20, 8); // Amount after fees
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled']);
            $table->text('description')->nullable(); // Transfer description
            $table->json('metadata')->nullable(); // Additional transfer data
            $table->timestamp('processed_at')->nullable(); // When transfer was processed
            $table->timestamp('failed_at')->nullable(); // When transfer failed
            $table->text('failure_reason')->nullable(); // Failure reason
            $table->timestamps();

            // Indexes
            $table->index(['reference']);
            $table->index(['from_customer_id']);
            $table->index(['to_customer_id']);
            $table->index(['from_account_number']);
            $table->index(['to_account_number']);
            $table->index(['currency']);
            $table->index(['status']);
            $table->index(['created_at']);
            $table->index(['processed_at']);

            // Composite indexes for common queries
            $table->index(['from_customer_id', 'status']);
            $table->index(['to_customer_id', 'status']);
            $table->index(['currency', 'status']);

            // Foreign key constraints
            $table->foreign('from_customer_id')->references('id')->on('customers')->onDelete('cascade');
            $table->foreign('to_customer_id')->references('id')->on('customers')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wallet_transfers');
    }
};
