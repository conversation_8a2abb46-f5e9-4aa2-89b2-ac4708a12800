<?php

use Illuminate\Support\Facades\Route;
use Thorne\Wallet\Http\Controllers\WalletController;
use <PERSON>\Wallet\Http\Controllers\DepositController;
use <PERSON>\Wallet\Http\Controllers\WithdrawController;
use <PERSON>\Wallet\Http\Controllers\TransferController;

/*
|--------------------------------------------------------------------------
| Wallet Web Routes
|--------------------------------------------------------------------------
|
| Here are the web routes for the wallet package. These routes are loaded
| by the WalletServiceProvider and will be assigned to the "web" middleware group.
|
*/

Route::middleware(['web', 'customer'])
    ->prefix('customer/account/wallet')
    ->name('customer.account.wallet.')
    ->group(function () {
        
        // Main wallet overview
        Route::get('/', [WalletController::class, 'index'])->name('index');
        Route::get('/overview', [WalletController::class, 'overview'])->name('overview');
        Route::get('/balance', [WalletController::class, 'balance'])->name('balance');
        Route::post('/sync-balance', [WalletController::class, 'syncBalance'])->name('sync-balance');
        
        // Account management
        Route::get('/accounts', [WalletController::class, 'accounts'])->name('accounts');
        Route::post('/accounts/create', [WalletController::class, 'createAccount'])->name('accounts.create');
        
        // Transaction history
        Route::get('/transactions', [WalletController::class, 'transactions'])->name('transactions');
        Route::get('/transactions/{transaction}', [WalletController::class, 'showTransaction'])->name('transactions.show');
        
        // Deposits
        Route::prefix('deposit')->name('deposit.')->group(function () {
            Route::get('/', [DepositController::class, 'index'])->name('index');
            Route::get('/methods', [DepositController::class, 'methods'])->name('methods');
            Route::get('/create', [DepositController::class, 'create'])->name('create');
            Route::post('/create', [DepositController::class, 'store'])->name('store');
            Route::get('/company-accounts', [DepositController::class, 'companyAccounts'])->name('company-accounts');
            Route::get('/{transaction}', [DepositController::class, 'show'])->name('show');
        });
        
        // Withdrawals
        Route::prefix('withdraw')->name('withdraw.')->group(function () {
            Route::get('/', [WithdrawController::class, 'index'])->name('index');
            Route::get('/methods', [WithdrawController::class, 'methods'])->name('methods');
            Route::get('/create', [WithdrawController::class, 'create'])->name('create');
            Route::post('/create', [WithdrawController::class, 'store'])->name('store');
            Route::get('/{transaction}', [WithdrawController::class, 'show'])->name('show');
        });
        
        // Internal transfers
        Route::prefix('transfer')->name('transfer.')->group(function () {
            Route::get('/', [TransferController::class, 'index'])->name('index');
            Route::get('/create', [TransferController::class, 'create'])->name('create');
            Route::post('/create', [TransferController::class, 'store'])->name('store');
            Route::get('/validate-account', [TransferController::class, 'validateAccount'])->name('validate-account');
            Route::get('/{transfer}', [TransferController::class, 'show'])->name('show');
        });
    });
