@extends('fast-order::layouts.miligold-default')

@section('body')
    <div class="account-content">
        <div class="account-layout">
            <div class="account-head">
                <span class="account-heading">
                    {{ __('wallet::app.wallet.title') }}
                </span>

                <div class="account-action">
                    <button type="button" class="btn btn-primary" @click="syncAllBalances">
                        {{ __('wallet::app.wallet.sync_balances') }}
                    </button>
                </div>

                <div class="horizontal-rule"></div>
            </div>

            <div class="account-table-content">
                <wallet-overview></wallet-overview>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/vue@2.7.14"></script>
    <script type="text/x-template" id="wallet-overview-template">
        <div class="wallet-overview">
            <!-- Balance Cards -->
            <div class="row mb-4">
                <div class="col-12">
                    <h3>{{ __('wallet::app.wallet.balances') }}</h3>
                </div>
                <div v-for="(balance, currency) in balances" :key="currency" class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">@{{ currency }}</h5>
                            <p class="card-text">
                                <strong>{{ __('wallet::app.wallet.balance') }}:</strong> @{{ formatAmount(balance.balance, currency) }}<br>
                                <strong>{{ __('wallet::app.wallet.available') }}:</strong> @{{ formatAmount(balance.available_balance, currency) }}<br>
                                <small class="text-muted" v-if="balance.last_sync">{{ __('wallet::app.wallet.last_sync') }}: @{{ formatDate(balance.last_sync) }}</small>
                            </p>
                            <button class="btn btn-sm btn-outline-primary" @click="syncBalance(currency)" :disabled="syncing">
                                <span v-if="syncing">{{ __('wallet::app.wallet.syncing') }}...</span>
                                <span v-else>{{ __('wallet::app.wallet.sync') }}</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <h3>{{ __('wallet::app.wallet.quick_actions') }}</h3>
                </div>
                <div class="col-md-3 mb-2">
                    <a href="#" class="btn btn-success btn-block" onclick="alert('Deposit feature coming soon')">
                        {{ __('wallet::app.wallet.deposit') }}
                    </a>
                </div>
                <div class="col-md-3 mb-2">
                    <a href="#" class="btn btn-warning btn-block" onclick="alert('Withdraw feature coming soon')">
                        {{ __('wallet::app.wallet.withdraw') }}
                    </a>
                </div>
                <div class="col-md-3 mb-2">
                    <a href="#" class="btn btn-info btn-block" onclick="alert('Transfer feature coming soon')">
                        {{ __('wallet::app.wallet.transfer') }}
                    </a>
                </div>
                <div class="col-md-3 mb-2">
                    <a href="#" class="btn btn-secondary btn-block" onclick="alert('Transaction history coming soon')">
                        {{ __('wallet::app.wallet.history') }}
                    </a>
                </div>
            </div>

            <!-- Loading State -->
            <div v-if="loading" class="text-center">
                <div class="spinner-border" role="status">
                    <span class="sr-only">{{ __('wallet::app.wallet.loading') }}</span>
                </div>
            </div>
        </div>
    </script>

    <script>
        Vue.component('wallet-overview', {
            template: '#wallet-overview-template',

            data() {
                return {
                    overview: @json($overview),
                    balances: @json($balances),
                    loading: false,
                    syncing: false
                };
            },

            methods: {
                async syncBalance(currency) {
                    this.syncing = true;

                    try {
                        const response = await axios.post('{{ route("customer.account.wallet.sync-balance") }}', {
                            currency: currency
                        });

                        if (response.data.success) {
                            // Update the specific currency balance
                            this.balances[currency] = {
                                ...this.balances[currency],
                                ...response.data.data
                            };

                            alert('Balance synced successfully!');
                        } else {
                            alert('Sync failed: ' + (response.data.message || 'Unknown error'));
                        }
                    } catch (error) {
                        console.error('Sync error:', error);
                        this.$toast.error('{{ __("wallet::app.wallet.sync_failed") }}');
                    } finally {
                        this.syncing = false;
                    }
                },

                formatAmount(amount, currency) {
                    const formatter = new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: currency === 'MLGR' ? 'USD' : currency,
                        minimumFractionDigits: currency === 'MLGR' ? 0 : 2,
                        maximumFractionDigits: currency === 'MLGR' ? 0 : 8
                    });

                    if (currency === 'MLGR') {
                        return amount + ' MLGR';
                    }

                    return formatter.format(parseFloat(amount));
                },

                formatDate(dateString) {
                    return new Date(dateString).toLocaleString();
                },

                async syncAllBalances() {
                    this.loading = true;

                    try {
                        const response = await axios.get('{{ route("customer.account.wallet.overview") }}?sync=1');

                        if (response.data.success) {
                            this.overview = response.data.data;
                            this.$toast.success('{{ __("wallet::app.wallet.all_balances_synced") }}');
                        } else {
                            this.$toast.error('{{ __("wallet::app.wallet.sync_failed") }}');
                        }
                    } catch (error) {
                        console.error('Sync error:', error);
                        this.$toast.error('{{ __("wallet::app.wallet.sync_failed") }}');
                    } finally {
                        this.loading = false;
                    }
                }
            }
        });
    </script>
@endpush
