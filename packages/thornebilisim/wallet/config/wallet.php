<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Wallet System Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for the Thorne Bilişim Wallet
    | management system including multi-currency support, payment methods,
    | and authorization settings.
    |
    */

    'enabled' => env('WALLET_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | Supported Currencies
    |--------------------------------------------------------------------------
    |
    | Define the currencies supported by the wallet system. Each currency
    | includes ISO code, name, symbol, and account number generation settings.
    |
    */
    'currencies' => [
        'EUR' => [
            'iso_code' => '978',
            'name' => 'Euro',
            'symbol' => '€',
            'is_default' => true,
            'precision' => 2,
            'enabled' => true,
        ],
        'USD' => [
            'iso_code' => '840',
            'name' => 'USD',
            'symbol' => '$',
            'is_default' => false,
            'precision' => 2,
            'enabled' => true,
        ],
        'MLGR' => [
            'iso_code' => '999',
            'name' => 'miliGRAM',
            'symbol' => 'MLGR',
            'is_default' => false,
            'precision' => 8,
            'enabled' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Account Number Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for generating 11-digit account numbers using Luhn algorithm.
    | Format: [Currency ISO Code (3)] + [Check Digit (1)] + [Random (7)]
    |
    */
    'account_number' => [
        'total_length' => 11,
        'currency_code_length' => 3,
        'check_digit_length' => 1,
        'random_length' => 7,
        'use_luhn_algorithm' => true,
        'max_generation_attempts' => 100,
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Methods Configuration
    |--------------------------------------------------------------------------
    |
    | Define available payment methods for deposits and withdrawals.
    | Each method can be enabled/disabled for specific operations.
    |
    */
    'payment_methods' => [
        'cash' => [
            'name' => 'Cash',
            'enabled' => env('WALLET_CASH_ENABLED', false),
            'can_deposit' => false,
            'can_withdraw' => false,
            'supported_currencies' => ['EUR', 'USD'],
            'config' => [],
        ],
        'bank_transfer' => [
            'name' => 'Bank Transfer',
            'enabled' => env('WALLET_BANK_TRANSFER_ENABLED', true),
            'can_deposit' => true,
            'can_withdraw' => true,
            'supported_currencies' => ['EUR', 'USD'],
            'config' => [
                'min_amount' => 10.00,
                'max_amount' => 50000.00,
                'processing_time' => '1-3 business days',
            ],
        ],
        'qonto' => [
            'name' => 'Qonto',
            'enabled' => env('WALLET_QONTO_ENABLED', true),
            'can_deposit' => true,
            'can_withdraw' => true,
            'supported_currencies' => ['EUR'],
            'config' => [
                'min_amount' => 1.00,
                'max_amount' => 10000.00,
                'processing_time' => 'Instant',
            ],
        ],
        'paypal' => [
            'name' => 'PayPal',
            'enabled' => env('WALLET_PAYPAL_ENABLED', false),
            'can_deposit' => true,
            'can_withdraw' => false, // Example: Can deposit but not withdraw
            'supported_currencies' => ['EUR', 'USD'],
            'config' => [
                'min_amount' => 5.00,
                'max_amount' => 5000.00,
                'processing_time' => 'Instant',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Authorization & Permissions
    |--------------------------------------------------------------------------
    |
    | Configure permission-based restrictions for wallet operations.
    | These settings can be overridden per customer or payment method.
    |
    */
    'permissions' => [
        'deposit' => [
            'enabled' => true,
            'require_kyc' => env('WALLET_DEPOSIT_REQUIRE_KYC', true),
            'daily_limit' => env('WALLET_DEPOSIT_DAILY_LIMIT', 10000.00),
            'monthly_limit' => env('WALLET_DEPOSIT_MONTHLY_LIMIT', 50000.00),
        ],
        'withdraw' => [
            'enabled' => true,
            'require_kyc' => env('WALLET_WITHDRAW_REQUIRE_KYC', true),
            'daily_limit' => env('WALLET_WITHDRAW_DAILY_LIMIT', 5000.00),
            'monthly_limit' => env('WALLET_WITHDRAW_MONTHLY_LIMIT', 25000.00),
            'require_approval' => env('WALLET_WITHDRAW_REQUIRE_APPROVAL', false),
        ],
        'transfer' => [
            'enabled' => true,
            'require_kyc' => env('WALLET_TRANSFER_REQUIRE_KYC', true),
            'daily_limit' => env('WALLET_TRANSFER_DAILY_LIMIT', 2000.00),
            'monthly_limit' => env('WALLET_TRANSFER_MONTHLY_LIMIT', 10000.00),
            'min_amount' => env('WALLET_TRANSFER_MIN_AMOUNT', 1.00),
            'max_amount' => env('WALLET_TRANSFER_MAX_AMOUNT', 1000.00),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | WEB3 Integration
    |--------------------------------------------------------------------------
    |
    | Configuration for WEB3 service integration for balance management.
    |
    */
    'web3' => [
        'enabled' => env('WALLET_WEB3_ENABLED', true),
        'base_url' => env('WALLET_WEB3_BASE_URL', 'http://134.209.247.1:16060'),
        'tenant_id' => env('WALLET_WEB3_TENANT_ID', 50),
        'chain_id' => env('WALLET_WEB3_CHAIN_ID', 'mirum-testnet'),
        'cache_ttl' => env('WALLET_WEB3_CACHE_TTL', 1800),
        'timeout' => env('WALLET_WEB3_TIMEOUT', 30),
        'retry_attempts' => env('WALLET_WEB3_RETRY_ATTEMPTS', 3),
    ],

    /*
    |--------------------------------------------------------------------------
    | Company Account Information
    |--------------------------------------------------------------------------
    |
    | Static company account information for deposits. Multiple accounts per
    | currency are supported. Each account should have a unique 'id' field.
    |
    */
    'company_accounts' => [
        'bank_transfer' => [
            'EUR' => [
                [
                    'id' => 'eur_primary',
                    'name' => 'Primary EUR Account',
                    'bank_name' => 'Example Bank',
                    'account_holder' => 'Thorne Bilişim Ltd.',
                    'iban' => 'TR00 0000 0000 0000 0000 0000 00',
                    'swift' => 'EXAMPLETR',
                    'currency' => 'EUR',
                    'reference_required' => true,
                    'is_active' => true,
                    'is_primary' => true,
                    'description' => 'Primary EUR account for deposits',
                ],
                [
                    'id' => 'eur_secondary',
                    'name' => 'Secondary EUR Account',
                    'bank_name' => 'Another Bank',
                    'account_holder' => 'Thorne Bilişim Ltd.',
                    'iban' => 'TR11 1111 1111 1111 1111 1111 11',
                    'swift' => 'ANOTHERTR',
                    'currency' => 'EUR',
                    'reference_required' => true,
                    'is_active' => true,
                    'is_primary' => false,
                    'description' => 'Secondary EUR account for high volume deposits',
                ],
            ],
            'USD' => [
                [
                    'id' => 'usd_primary',
                    'name' => 'Primary USD Account',
                    'bank_name' => 'Example Bank USD',
                    'account_holder' => 'Thorne Bilişim Ltd.',
                    'account_number' => '*********0',
                    'routing_number' => '*********',
                    'swift' => 'EXAMPLEUS',
                    'currency' => 'USD',
                    'reference_required' => true,
                    'is_active' => true,
                    'is_primary' => true,
                    'description' => 'Primary USD account for deposits',
                ],
            ],
            'MLGR' => [
                [
                    'id' => 'mlgr_primary',
                    'name' => 'Primary MLGR Account',
                    'bank_name' => 'Crypto Exchange',
                    'account_holder' => 'Thorne Bilişim Ltd.',
                    'wallet_address' => 'mirum1wluflpv6g3ygtqj0sfwgvclmdwgfuxmhsdxafh',
                    'currency' => 'MLGR',
                    'reference_required' => false,
                    'is_active' => true,
                    'is_primary' => true,
                    'description' => 'Primary MLGR wallet for deposits',
                ],
            ],
        ],
        'paypal' => [
            'EUR' => [
                [
                    'id' => 'paypal_eur',
                    'name' => 'PayPal EUR Account',
                    'email' => '<EMAIL>',
                    'currency' => 'EUR',
                    'reference_required' => false,
                    'is_active' => true,
                    'is_primary' => true,
                    'description' => 'PayPal account for EUR deposits',
                ],
            ],
            'USD' => [
                [
                    'id' => 'paypal_usd',
                    'name' => 'PayPal USD Account',
                    'email' => '<EMAIL>',
                    'currency' => 'USD',
                    'reference_required' => false,
                    'is_active' => true,
                    'is_primary' => true,
                    'description' => 'PayPal account for USD deposits',
                ],
            ],
        ],
        'qonto' => [
            'EUR' => [
                [
                    'id' => 'qonto_eur',
                    'name' => 'Qonto EUR Account',
                    'bank_name' => 'Qonto',
                    'account_holder' => 'Thorne Bilişim Ltd.',
                    'iban' => 'FR76 1234 5678 9012 3456 7890 123',
                    'swift' => 'QNTOFRP1',
                    'currency' => 'EUR',
                    'reference_required' => true,
                    'is_active' => true,
                    'is_primary' => true,
                    'description' => 'Qonto business account for EUR deposits',
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Transaction Settings
    |--------------------------------------------------------------------------
    |
    | General transaction settings and limits.
    |
    */
    'transactions' => [
        'reference_prefix' => env('WALLET_REFERENCE_PREFIX', 'WLT-'),
        'pending_timeout' => env('WALLET_PENDING_TIMEOUT', 24), // hours
        'auto_approve_deposits' => env('WALLET_AUTO_APPROVE_DEPOSITS', false),
        'auto_approve_withdrawals' => env('WALLET_AUTO_APPROVE_WITHDRAWALS', false),
        'require_unique_reference' => env('WALLET_REQUIRE_UNIQUE_REFERENCE', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Configure logging for wallet operations.
    |
    */
    'logging' => [
        'enabled' => env('WALLET_LOGGING_ENABLED', true),
        'channel' => env('WALLET_LOG_CHANNEL', 'wallet'),
        'level' => env('WALLET_LOG_LEVEL', 'info'),
        'log_balance_checks' => env('WALLET_LOG_BALANCE_CHECKS', true),
        'log_transactions' => env('WALLET_LOG_TRANSACTIONS', true),
    ],
];
