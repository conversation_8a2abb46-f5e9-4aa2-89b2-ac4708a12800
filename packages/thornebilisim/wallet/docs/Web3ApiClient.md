# Web3ApiClient Documentation

## Overview

The `Web3ApiClient` is a comprehensive HTTP client specifically designed for making authenticated requests to Web3 services. It provides OAuth 1.0 authentication, automatic retry logic, comprehensive logging, and a fluent interface for easy usage.

## Features

- **OAuth 1.0 Authentication**: Automatic signature generation with RSA-SHA256
- **Retry Logic**: Configurable retry attempts with exponential backoff
- **Comprehensive Logging**: Detailed request/response logging with sanitization
- **Fluent Interface**: Chainable method calls for easy configuration
- **Error Handling**: Custom exceptions with context information
- **Timeout Management**: Configurable request timeouts
- **Header Management**: Custom headers support
- **Query Parameters**: Easy query parameter handling

## Installation

The Web3ApiClient is automatically registered when you install the wallet package. No additional installation steps are required.

## Configuration

Add the following configuration to your `.env` file:

```env
# Web3 API Configuration
WALLET_WEB3_ENABLED=true
WALLET_WEB3_BASE_URL=http://134.209.247.1:16060
WALLET_WEB3_TENANT_ID=50
WALLET_WEB3_CHAIN_ID=mirum-testnet
WALLET_WEB3_CACHE_TTL=1800
WALLET_WEB3_TIMEOUT=30
WALLET_WEB3_RETRY_ATTEMPTS=3

# OAuth Configuration
OAUTH_CONSUMER_KEY=your_consumer_key

# Logging Configuration
WALLET_LOGGING_ENABLED=true
WALLET_LOG_CHANNEL=wallet
WALLET_LOG_LEVEL=info
```

## Private Key Setup

Place your private key file at `base_path('private-key.pem')` for OAuth 1.0 signature generation.

## Basic Usage

### Using the Service Class

```php
use Thorne\Wallet\Services\Web3ApiClient;

// Basic POST request
$response = Web3ApiClient::make()
    ->endpoint('/wallet/balance')
    ->post([
        'userId' => 4481,
        'tenantId' => 50,
        'chainId' => 'mirum-testnet'
    ]);

$data = $response->json();
```

### Using the Facade

```php
use Thorne\Wallet\Facades\Web3ApiClient as Web3;

// Same request using facade
$response = Web3::endpoint('/wallet/balance')
    ->post([
        'userId' => 4481,
        'tenantId' => 50,
        'chainId' => 'mirum-testnet'
    ]);
```

### Using Dependency Injection

```php
use Thorne\Wallet\Services\Web3ApiClient;

class YourService
{
    public function __construct(private Web3ApiClient $web3Client)
    {
    }

    public function getBalance(int $customerId): array
    {
        $response = $this->web3Client
            ->endpoint('/wallet/balance')
            ->post([
                'userId' => $customerId,
                'tenantId' => 50,
                'chainId' => 'mirum-testnet'
            ]);

        return $response->json();
    }
}
```

## Advanced Usage

### Custom Configuration

```php
$response = Web3ApiClient::make()
    ->endpoint('/wallet/balance')
    ->timeout(60)           // 60 seconds timeout
    ->retries(5)            // 5 retry attempts
    ->withHeaders([
        'X-Custom-Header' => 'value',
        'X-Request-ID' => uniqid()
    ])
    ->post($data);
```

### Different HTTP Methods

```php
// GET request with query parameters
$response = Web3ApiClient::make()
    ->endpoint('/wallet/transactions')
    ->withQuery([
        'userId' => 4481,
        'limit' => 50
    ])
    ->get();

// PUT request
$response = Web3ApiClient::make()
    ->endpoint('/wallet/update')
    ->put(['status' => 'active']);

// DELETE request
$response = Web3ApiClient::make()
    ->endpoint('/wallet/delete')
    ->delete(['userId' => 4481]);
```

### Silent Requests (No Logging)

```php
$response = Web3ApiClient::make()
    ->endpoint('/wallet/balance')
    ->withoutLogging()
    ->post($data);
```

## Method Reference

### Configuration Methods

- `endpoint(string $path)`: Set the API endpoint path
- `method(string $method)`: Set HTTP method (GET, POST, PUT, DELETE)
- `timeout(int $timeout)`: Set request timeout in seconds
- `retries(int $attempts)`: Set retry attempts
- `withHeaders(array $headers)`: Add custom headers
- `withQuery(array $params)`: Add query parameters
- `withoutLogging()`: Disable logging for this request

### HTTP Methods

- `get(array $query = [])`: Make GET request
- `post(array $data = [])`: Make POST request
- `put(array $data = [])`: Make PUT request
- `delete(array $data = [])`: Make DELETE request
- `send(array $data = [])`: Generic send method

### Static Methods

- `make()`: Create new instance for fluent interface

## Error Handling

```php
use Thorne\Wallet\Exceptions\Web3ApiException;

try {
    $response = Web3ApiClient::make()
        ->endpoint('/wallet/balance')
        ->post($data);

    if ($response->successful()) {
        return $response->json();
    } else {
        throw new \RuntimeException("API request failed: {$response->status()}");
    }

} catch (Web3ApiException $e) {
    // Handle Web3 API specific errors
    Log::error('Web3 API Error', [
        'message' => $e->getMessage(),
        'context' => $e->getContext(),
    ]);
    
    throw $e;
} catch (\Exception $e) {
    // Handle general errors
    Log::error('General Error', ['message' => $e->getMessage()]);
    throw $e;
}
```

## Response Handling

```php
$response = Web3ApiClient::make()
    ->endpoint('/wallet/balance')
    ->post($data);

// Check if successful
if ($response->successful()) {
    $data = $response->json();
    $status = $response->status();
    $headers = $response->headers();
}

// Handle different status codes
switch ($response->status()) {
    case 200:
        // Success
        break;
    case 400:
        // Bad request
        break;
    case 401:
        // Unauthorized
        break;
    case 500:
        // Server error
        break;
}
```

## Logging

The Web3ApiClient automatically logs:

- Request details (URL, method, headers, data)
- Response details (status, headers, body, duration)
- Error details (message, code, trace)
- Retry attempts

Logs are written to the configured wallet log channel with the following structure:

```json
{
    "timestamp": "2024-06-02 10:30:45",
    "level": "INFO",
    "message": "Web3 API Request",
    "context": {
        "request_id": "web3_64a1b2c3d4e5f",
        "method": "POST",
        "url": "http://134.209.247.1:16060/wallet/balance",
        "headers": {
            "Content-Type": "application/json",
            "Authorization": "[REDACTED]"
        },
        "data": {
            "userId": 4481,
            "tenantId": 50,
            "chainId": "mirum-testnet"
        }
    },
    "customer_id": 4481,
    "service": "wallet"
}
```

## Security

- OAuth 1.0 signatures are automatically generated
- Authorization headers are redacted in logs
- Private key is securely loaded from file system
- Request/response data is logged but can be disabled per request

## Performance

- Automatic retry with exponential backoff
- Configurable timeouts
- Connection reuse through Laravel HTTP client
- Efficient logging with context extraction

## Integration with Existing Code

Replace your existing Web3 API calls:

```php
// Old way
$response = Http::timeout($timeout)
    ->post($baseUrl . '/wallet/balance', $payload);

// New way
$response = Web3ApiClient::make()
    ->endpoint('/wallet/balance')
    ->post($payload);
```

The new client handles authentication, retries, and logging automatically.

## Troubleshooting

### Common Issues

1. **Private Key Not Found**
   - Ensure `private-key.pem` exists in project root
   - Check file permissions

2. **OAuth Signature Errors**
   - Verify consumer key configuration
   - Check private key format

3. **Connection Timeouts**
   - Increase timeout value
   - Check network connectivity

4. **Logging Issues**
   - Verify wallet log channel configuration
   - Check log file permissions

### Debug Mode

Enable debug logging by setting `WALLET_LOG_LEVEL=debug` in your `.env` file.
