# Wallet Management API

Bu dokümantasyon, wallet yönetimi ile ilgili API endpoint'lerini detaylı olarak açıklar.

## 📋 Endpoints

### 1. Get Wallet Overview

Müşterinin tüm wallet bilgilerini tek seferde getirir.

**Endpoint:** `GET /api/wallet/overview`

**Authentication:** Bearer <PERSON>ken Required

**Headers:**
```http
Authorization: Bearer {your-token}
Content-Type: application/json
Accept: application/json
```

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| include_inactive | boolean | No | Pasif hesapları da dahil et (default: false) |
| currency | string | No | Belirli para birimini filtrele |

**Example Request:**
```bash
curl -X GET "https://api.example.com/api/wallet/overview?include_inactive=true" \
  -H "Authorization: Bearer your-token-here" \
  -H "Accept: application/json"
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Wallet overview retrieved successfully",
    "data": {
        "customer_id": 123,
        "total_balance_usd": "3500.50",
        "accounts": [
            {
                "id": 1,
                "account_number": "***********",
                "currency": "EUR",
                "currency_symbol": "€",
                "currency_name": "Euro",
                "iso_code": "978",
                "is_active": true,
                "is_default": true,
                "balance": {
                    "amount": "1500.50",
                    "formatted": "€1,500.50",
                    "precision": 2
                },
                "created_at": "2024-01-01T00:00:00.000000Z",
                "updated_at": "2024-01-01T12:00:00.000000Z"
            },
            {
                "id": 2,
                "account_number": "***********",
                "currency": "USD",
                "currency_symbol": "$",
                "currency_name": "US Dollar",
                "iso_code": "840",
                "is_active": true,
                "is_default": false,
                "balance": {
                    "amount": "2000.00",
                    "formatted": "$2,000.00",
                    "precision": 2
                },
                "created_at": "2024-01-01T00:00:00.000000Z",
                "updated_at": "2024-01-01T12:00:00.000000Z"
            }
        ],
        "balances": [
            {
                "currency": "EUR",
                "balance": "1500.50",
                "formatted_balance": "€1,500.50",
                "usd_equivalent": "1650.55",
                "last_synced": "2024-01-01T12:00:00.000000Z",
                "sync_status": "success"
            },
            {
                "currency": "USD",
                "balance": "2000.00",
                "formatted_balance": "$2,000.00",
                "usd_equivalent": "2000.00",
                "last_synced": "2024-01-01T12:00:00.000000Z",
                "sync_status": "success"
            }
        ],
        "recent_transactions": [
            {
                "id": 1,
                "reference": "TXN-********-001",
                "type": "deposit",
                "direction": "credit",
                "amount": "500.00",
                "currency": "EUR",
                "status": "completed",
                "description": "Bank transfer deposit",
                "created_at": "2024-01-01T10:00:00.000000Z",
                "completed_at": "2024-01-01T10:05:00.000000Z"
            }
        ],
        "statistics": {
            "total_transactions": 25,
            "total_deposits": "5000.00",
            "total_withdrawals": "2000.00",
            "total_transfers_sent": "1500.00",
            "total_transfers_received": "1000.00"
        }
    }
}
```

**Error Responses:**

**401 Unauthorized:**
```json
{
    "success": false,
    "message": "Unauthenticated.",
    "errors": {
        "auth": ["Token is invalid or expired"]
    }
}
```

**500 Internal Server Error:**
```json
{
    "success": false,
    "message": "Failed to retrieve wallet overview",
    "errors": {
        "system": ["Database connection failed"]
    }
}
```

---

### 2. Get Account Details

Belirli bir hesabın detaylı bilgilerini getirir.

**Endpoint:** `GET /api/wallet/accounts/{account_number}`

**Authentication:** Bearer Token Required

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| account_number | string | Yes | 11 haneli hesap numarası (Luhn algoritması ile doğrulanmış) |

**Example Request:**
```bash
curl -X GET "https://api.example.com/api/wallet/accounts/***********" \
  -H "Authorization: Bearer your-token-here" \
  -H "Accept: application/json"
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Account details retrieved successfully",
    "data": {
        "id": 1,
        "account_number": "***********",
        "currency": "EUR",
        "currency_symbol": "€",
        "currency_name": "Euro",
        "iso_code": "978",
        "is_active": true,
        "is_default": true,
        "balance": {
            "amount": "1500.50",
            "formatted": "€1,500.50",
            "precision": 2,
            "last_synced": "2024-01-01T12:00:00.000000Z",
            "sync_status": "success",
            "web3_balance": "1500.50",
            "db_balance": "1500.50",
            "is_synced": true
        },
        "account_details": {
            "luhn_valid": true,
            "currency_code": "978",
            "check_digit": "4",
            "random_part": "1234567"
        },
        "limits": {
            "daily_transfer_limit": "10000.00",
            "monthly_transfer_limit": "50000.00",
            "daily_withdrawal_limit": "5000.00",
            "monthly_withdrawal_limit": "25000.00"
        },
        "permissions": {
            "can_deposit": true,
            "can_withdraw": true,
            "can_transfer": true,
            "can_receive": true
        },
        "created_at": "2024-01-01T00:00:00.000000Z",
        "updated_at": "2024-01-01T12:00:00.000000Z"
    }
}
```

**Error Responses:**

**404 Not Found:**
```json
{
    "success": false,
    "message": "Account not found",
    "errors": {
        "account_number": ["Account with number *********** does not exist"]
    }
}
```

**422 Validation Error:**
```json
{
    "success": false,
    "message": "Invalid account number format",
    "errors": {
        "account_number": [
            "Account number must be 11 digits",
            "Account number failed Luhn validation"
        ]
    }
}
```

---

### 3. Create New Account

Yeni para birimi hesabı oluşturur.

**Endpoint:** `POST /api/wallet/accounts`

**Authentication:** Bearer Token Required

**Request Body:**
```json
{
    "currency": "MLGR",
    "is_default": false
}
```

**Request Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| currency | string | Yes | Para birimi kodu (EUR, USD, MLGR) |
| is_default | boolean | No | Varsayılan hesap olarak ayarla (default: false) |

**Example Request:**
```bash
curl -X POST "https://api.example.com/api/wallet/accounts" \
  -H "Authorization: Bearer your-token-here" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "currency": "MLGR",
    "is_default": false
  }'
```

**Success Response (201):**
```json
{
    "success": true,
    "message": "Account created successfully",
    "data": {
        "id": 3,
        "account_number": "***********",
        "currency": "MLGR",
        "currency_symbol": "MLGR",
        "currency_name": "Miligram",
        "iso_code": "999",
        "is_active": true,
        "is_default": false,
        "balance": {
            "amount": "0.********",
            "formatted": "0.******** MLGR",
            "precision": 8
        },
        "account_details": {
            "luhn_valid": true,
            "currency_code": "999",
            "check_digit": "5",
            "random_part": "1234567"
        },
        "created_at": "2024-01-01T13:00:00.000000Z",
        "updated_at": "2024-01-01T13:00:00.000000Z"
    }
}
```

**Error Responses:**

**422 Validation Error:**
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "currency": [
            "The currency field is required.",
            "The selected currency is invalid."
        ]
    }
}
```

**409 Conflict:**
```json
{
    "success": false,
    "message": "Account already exists",
    "errors": {
        "currency": ["You already have an account for this currency"]
    }
}
```

---

### 4. Update Account Settings

Hesap ayarlarını günceller.

**Endpoint:** `PUT /api/wallet/accounts/{account_number}`

**Authentication:** Bearer Token Required

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| account_number | string | Yes | 11 haneli hesap numarası |

**Request Body:**
```json
{
    "is_default": true,
    "is_active": true
}
```

**Request Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| is_default | boolean | No | Varsayılan hesap olarak ayarla |
| is_active | boolean | No | Hesabı aktif/pasif yap |

**Example Request:**
```bash
curl -X PUT "https://api.example.com/api/wallet/accounts/***********" \
  -H "Authorization: Bearer your-token-here" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "is_default": true,
    "is_active": true
  }'
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Account updated successfully",
    "data": {
        "id": 1,
        "account_number": "***********",
        "currency": "EUR",
        "is_active": true,
        "is_default": true,
        "updated_at": "2024-01-01T14:00:00.000000Z"
    }
}
```

---

### 5. Get Transaction History

Hesap işlem geçmişini getirir.

**Endpoint:** `GET /api/wallet/transactions`

**Authentication:** Bearer Token Required

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| account_number | string | No | Hesap numarası filtresi |
| currency | string | No | Para birimi filtresi |
| type | string | No | İşlem tipi (deposit, withdrawal, transfer) |
| direction | string | No | İşlem yönü (credit, debit) |
| status | string | No | İşlem durumu (pending, completed, failed, cancelled) |
| method | string | No | İşlem yöntemi |
| date_from | string | No | Başlangıç tarihi (Y-m-d format) |
| date_to | string | No | Bitiş tarihi (Y-m-d format) |
| amount_min | decimal | No | Minimum tutar |
| amount_max | decimal | No | Maksimum tutar |
| per_page | integer | No | Sayfa başına kayıt (default: 15, max: 100) |
| page | integer | No | Sayfa numarası |
| sort_by | string | No | Sıralama alanı (created_at, amount, status) |
| sort_order | string | No | Sıralama yönü (asc, desc) |

**Example Request:**
```bash
curl -X GET "https://api.example.com/api/wallet/transactions?currency=EUR&type=deposit&per_page=20&sort_by=created_at&sort_order=desc" \
  -H "Authorization: Bearer your-token-here" \
  -H "Accept: application/json"
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Transaction history retrieved successfully",
    "data": {
        "current_page": 1,
        "data": [
            {
                "id": 1,
                "reference": "TXN-********-001",
                "type": "deposit",
                "direction": "credit",
                "amount": "500.00",
                "currency": "EUR",
                "status": "completed",
                "method": "bank_transfer",
                "description": "Bank transfer deposit",
                "metadata": {
                    "bank_reference": "BNK123456",
                    "deposit_id": 1,
                    "processing_time": "00:05:00"
                },
                "fee": "0.00",
                "net_amount": "500.00",
                "balance_before": "1000.50",
                "balance_after": "1500.50",
                "created_at": "2024-01-01T10:00:00.000000Z",
                "completed_at": "2024-01-01T10:05:00.000000Z",
                "failed_at": null,
                "failure_reason": null
            },
            {
                "id": 2,
                "reference": "TXN-********-002",
                "type": "transfer",
                "direction": "debit",
                "amount": "-100.50",
                "currency": "EUR",
                "status": "completed",
                "method": "internal_transfer",
                "description": "Transfer to ***********",
                "metadata": {
                    "transfer_id": 1,
                    "to_account": "***********",
                    "transfer_fee": "0.50"
                },
                "fee": "0.50",
                "net_amount": "-100.50",
                "balance_before": "1500.50",
                "balance_after": "1400.00",
                "created_at": "2024-01-01T11:00:00.000000Z",
                "completed_at": "2024-01-01T11:00:01.000000Z",
                "failed_at": null,
                "failure_reason": null
            }
        ],
        "first_page_url": "https://api.example.com/api/wallet/transactions?page=1",
        "from": 1,
        "last_page": 5,
        "last_page_url": "https://api.example.com/api/wallet/transactions?page=5",
        "links": [
            {
                "url": null,
                "label": "&laquo; Previous",
                "active": false
            },
            {
                "url": "https://api.example.com/api/wallet/transactions?page=1",
                "label": "1",
                "active": true
            },
            {
                "url": "https://api.example.com/api/wallet/transactions?page=2",
                "label": "2",
                "active": false
            }
        ],
        "next_page_url": "https://api.example.com/api/wallet/transactions?page=2",
        "path": "https://api.example.com/api/wallet/transactions",
        "per_page": 20,
        "prev_page_url": null,
        "to": 20,
        "total": 95
    }
}
```

---

### 6. Sync Balance with WEB3

WEB3 servisi ile bakiye senkronizasyonu yapar.

**Endpoint:** `POST /api/wallet/sync-balance`

**Authentication:** Bearer Token Required

**Request Body:**
```json
{
    "currency": "EUR",
    "force": false
}
```

**Request Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| currency | string | No | Belirli para birimini senkronize et |
| force | boolean | No | Zorla senkronizasyon (default: false) |

**Example Request:**
```bash
curl -X POST "https://api.example.com/api/wallet/sync-balance" \
  -H "Authorization: Bearer your-token-here" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "currency": "EUR",
    "force": true
  }'
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Balance synchronized successfully",
    "data": {
        "synchronized_currencies": [
            {
                "currency": "EUR",
                "previous_balance": "1500.50",
                "new_balance": "1500.50",
                "web3_balance": "1500.50",
                "is_synced": true,
                "sync_timestamp": "2024-01-01T15:00:00.000000Z"
            }
        ],
        "sync_summary": {
            "total_currencies": 1,
            "successful_syncs": 1,
            "failed_syncs": 0,
            "sync_duration": "0.5s"
        }
    }
}
```

**Error Responses:**

**503 Service Unavailable:**
```json
{
    "success": false,
    "message": "WEB3 service unavailable",
    "errors": {
        "web3": ["Unable to connect to WEB3 service"]
    }
}
```

---

### 7. Get Account Statistics

Hesap istatistiklerini getirir.

**Endpoint:** `GET /api/wallet/accounts/{account_number}/statistics`

**Authentication:** Bearer Token Required

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| account_number | string | Yes | 11 haneli hesap numarası |

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| period | string | No | İstatistik dönemi (7d, 30d, 90d, 1y, all) |
| include_pending | boolean | No | Bekleyen işlemleri dahil et |

**Example Request:**
```bash
curl -X GET "https://api.example.com/api/wallet/accounts/***********/statistics?period=30d" \
  -H "Authorization: Bearer your-token-here" \
  -H "Accept: application/json"
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Account statistics retrieved successfully",
    "data": {
        "account_number": "***********",
        "currency": "EUR",
        "period": "30d",
        "statistics": {
            "total_transactions": 45,
            "total_deposits": {
                "count": 12,
                "amount": "5000.00",
                "average": "416.67"
            },
            "total_withdrawals": {
                "count": 8,
                "amount": "2000.00",
                "average": "250.00"
            },
            "total_transfers_sent": {
                "count": 15,
                "amount": "1500.00",
                "average": "100.00"
            },
            "total_transfers_received": {
                "count": 10,
                "amount": "1000.00",
                "average": "100.00"
            },
            "balance_changes": {
                "starting_balance": "1000.00",
                "ending_balance": "1500.50",
                "net_change": "+500.50",
                "percentage_change": "+50.05%"
            },
            "daily_averages": {
                "transactions_per_day": 1.5,
                "volume_per_day": "316.67"
            }
        },
        "chart_data": {
            "balance_history": [
                {
                    "date": "2024-01-01",
                    "balance": "1000.00"
                },
                {
                    "date": "2024-01-02",
                    "balance": "1200.00"
                }
            ],
            "transaction_volume": [
                {
                    "date": "2024-01-01",
                    "deposits": "500.00",
                    "withdrawals": "0.00",
                    "transfers": "100.00"
                }
            ]
        }
    }
}
```

## 🔍 Common Error Codes

| Error Code | Description | Solution |
|------------|-------------|----------|
| WALLET_001 | Invalid account number format | Ensure account number is 11 digits and passes Luhn validation |
| WALLET_002 | Account not found | Verify the account number exists for the authenticated customer |
| WALLET_003 | Currency not supported | Use supported currencies: EUR, USD, MLGR |
| WALLET_004 | Account already exists | Customer already has an account for this currency |
| WALLET_005 | WEB3 sync failed | WEB3 service is unavailable, try again later |
| WALLET_006 | Balance mismatch | Database and WEB3 balances don't match |
| WALLET_007 | Account inactive | Account is deactivated, contact support |
| WALLET_008 | Permission denied | Customer doesn't have permission for this operation |

## 📊 Rate Limits

| Endpoint | Rate Limit | Window |
|----------|------------|---------|
| GET /overview | 30 requests | 1 minute |
| GET /accounts/* | 60 requests | 1 minute |
| POST /accounts | 5 requests | 1 minute |
| PUT /accounts/* | 10 requests | 1 minute |
| GET /transactions | 100 requests | 1 minute |
| POST /sync-balance | 5 requests | 1 minute |
| GET /statistics | 20 requests | 1 minute |
