# Deposit Operations API

Bu dokümantasyon, para yatırma işlemleri ile ilgili API endpoint'lerini detaylı olarak açıklar.

## 📋 Endpoints

### 1. Get Available Deposit Methods

Mevcut para yatırma yöntemlerini ve detaylarını getirir.

**Endpoint:** `GET /api/wallet/deposit/methods`

**Authentication:** <PERSON><PERSON> Token Required

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| currency | string | No | Belirli para birimi için yöntemleri filtrele |
| enabled_only | boolean | No | Sadece aktif yöntemleri getir (default: true) |

**Example Request:**
```bash
curl -X GET "https://api.example.com/api/wallet/deposit/methods?currency=EUR" \
  -H "Authorization: Bearer your-token-here" \
  -H "Accept: application/json"
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Deposit methods retrieved successfully",
    "data": [
        {
            "method": "bank_transfer",
            "name": "Bank Transfer",
            "description": "Traditional bank wire transfer",
            "icon": "bank-icon.svg",
            "supported_currencies": ["EUR", "USD"],
            "limits": {
                "min_amount": "10.00",
                "max_amount": "50000.00",
                "daily_limit": "10000.00",
                "monthly_limit": "100000.00"
            },
            "fees": {
                "fixed": "0.00",
                "percentage": "0.00",
                "description": "No fees for bank transfers"
            },
            "processing_time": {
                "min_hours": 24,
                "max_hours": 72,
                "description": "1-3 business days"
            },
            "requirements": [
                "Valid bank account",
                "Matching account holder name"
            ],
            "is_enabled": true,
            "availability": {
                "24_7": false,
                "business_hours_only": true,
                "maintenance_windows": []
            }
        },
        {
            "method": "qonto",
            "name": "Qonto",
            "description": "Instant Qonto business account transfer",
            "icon": "qonto-icon.svg",
            "supported_currencies": ["EUR"],
            "limits": {
                "min_amount": "5.00",
                "max_amount": "10000.00",
                "daily_limit": "5000.00",
                "monthly_limit": "50000.00"
            },
            "fees": {
                "fixed": "0.50",
                "percentage": "0.00",
                "description": "€0.50 fixed fee per transaction"
            },
            "processing_time": {
                "min_hours": 0,
                "max_hours": 1,
                "description": "Instant to 1 hour"
            },
            "requirements": [
                "Qonto business account",
                "Verified business profile"
            ],
            "is_enabled": true,
            "availability": {
                "24_7": true,
                "business_hours_only": false,
                "maintenance_windows": [
                    {
                        "start": "02:00",
                        "end": "03:00",
                        "timezone": "UTC",
                        "description": "Daily maintenance"
                    }
                ]
            }
        },
        {
            "method": "paypal",
            "name": "PayPal",
            "description": "PayPal instant transfer",
            "icon": "paypal-icon.svg",
            "supported_currencies": ["EUR", "USD"],
            "limits": {
                "min_amount": "1.00",
                "max_amount": "2500.00",
                "daily_limit": "1000.00",
                "monthly_limit": "10000.00"
            },
            "fees": {
                "fixed": "0.30",
                "percentage": "2.90",
                "description": "€0.30 + 2.9% of transaction amount"
            },
            "processing_time": {
                "min_hours": 0,
                "max_hours": 0,
                "description": "Instant"
            },
            "requirements": [
                "Verified PayPal account",
                "Sufficient PayPal balance"
            ],
            "is_enabled": true,
            "availability": {
                "24_7": true,
                "business_hours_only": false,
                "maintenance_windows": []
            }
        }
    ]
}
```

---

### 2. Get Company Account Details

Para yatırma için şirket hesap bilgilerini getirir.

**Endpoint:** `GET /api/wallet/deposit/accounts/{method}`

**Authentication:** Bearer Token Required

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| method | string | Yes | Para yatırma yöntemi (bank_transfer, qonto, paypal) |

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| currency | string | Yes | Para birimi kodu |
| amount | decimal | No | Yatırılacak tutar (dinamik referans için) |

**Example Request:**
```bash
curl -X GET "https://api.example.com/api/wallet/deposit/accounts/bank_transfer?currency=EUR&amount=500.00" \
  -H "Authorization: Bearer your-token-here" \
  -H "Accept: application/json"
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Company account details retrieved successfully",
    "data": {
        "method": "bank_transfer",
        "currency": "EUR",
        "amount": "500.00",
        "accounts": [
            {
                "id": "primary_eur",
                "bank_name": "Deutsche Bank AG",
                "bank_code": "DEUTDEFF",
                "account_holder": "Thorne Bilişim Ltd.",
                "iban": "**********************",
                "bic": "DEUTDEFF",
                "swift": "DEUTDEFF",
                "address": {
                    "street": "Taunusanlage 12",
                    "city": "Frankfurt am Main",
                    "postal_code": "60325",
                    "country": "Germany"
                },
                "reference": "DEPOSIT-123456-EUR",
                "is_primary": true
            },
            {
                "id": "secondary_eur",
                "bank_name": "Commerzbank AG",
                "bank_code": "COBADEFF",
                "account_holder": "Thorne Bilişim Ltd.",
                "iban": "**********************",
                "bic": "COBADEFF",
                "swift": "COBADEFF",
                "address": {
                    "street": "Kaiserstraße 16",
                    "city": "Frankfurt am Main",
                    "postal_code": "60311",
                    "country": "Germany"
                },
                "reference": "DEPOSIT-123456-EUR",
                "is_primary": false
            }
        ],
        "instructions": [
            "Transfer exactly €500.00 to one of the accounts above",
            "Use the reference number: DEPOSIT-123456-EUR",
            "Ensure the sender name matches your account name",
            "Processing time: 1-3 business days",
            "Contact support if transfer is not processed within 5 business days"
        ],
        "important_notes": [
            "Do not send from third-party accounts",
            "Partial payments will be rejected",
            "Reference number is mandatory for automatic processing",
            "Transfers without reference may take up to 7 days to process"
        ],
        "fees": {
            "our_fee": "0.00",
            "bank_fee": "Variable (depends on your bank)",
            "total_to_send": "500.00"
        },
        "expires_at": "2024-01-08T14:00:00.000000Z",
        "qr_code": {
            "data": "BCD\n002\n1\nSCT\nDEUTDEFF\nThorne Bilişim Ltd.\n**********************\nEUR500.00\n\n\nDEPOSIT-123456-EUR",
            "image_url": "https://api.example.com/qr/deposit/DEPOSIT-123456-EUR.png"
        }
    }
}
```

**Error Responses:**

**422 Validation Error:**
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "method": ["The selected method is invalid"],
        "currency": ["Currency is not supported for this method"]
    }
}
```

---

### 3. Create Deposit Request

Yeni para yatırma talebi oluşturur.

**Endpoint:** `POST /api/wallet/deposit`

**Authentication:** Bearer Token Required

**Request Body:**
```json
{
    "method": "bank_transfer",
    "currency": "EUR",
    "amount": "500.00",
    "description": "Monthly deposit",
    "metadata": {
        "source": "salary",
        "category": "income"
    }
}
```

**Request Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| method | string | Yes | Para yatırma yöntemi |
| currency | string | Yes | Para birimi kodu |
| amount | decimal | Yes | Yatırılacak tutar |
| description | string | No | İşlem açıklaması |
| metadata | object | No | Ek bilgiler |

**Validation Rules:**
- `method`: Desteklenen yöntemlerden biri olmalı
- `currency`: Seçilen yöntem için desteklenen para birimi olmalı
- `amount`: Min/max limitler içinde olmalı
- `description`: Maksimum 255 karakter

**Example Request:**
```bash
curl -X POST "https://api.example.com/api/wallet/deposit" \
  -H "Authorization: Bearer your-token-here" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "method": "bank_transfer",
    "currency": "EUR",
    "amount": "500.00",
    "description": "Monthly deposit",
    "metadata": {
      "source": "salary",
      "category": "income"
    }
  }'
```

**Success Response (201):**
```json
{
    "success": true,
    "message": "Deposit request created successfully",
    "data": {
        "id": 1,
        "reference": "DEP-************",
        "method": "bank_transfer",
        "currency": "EUR",
        "amount": "500.00",
        "status": "pending",
        "description": "Monthly deposit",
        "metadata": {
            "source": "salary",
            "category": "income"
        },
        "company_account": {
            "bank_name": "Deutsche Bank AG",
            "account_holder": "Thorne Bilişim Ltd.",
            "iban": "**********************",
            "bic": "DEUTDEFF",
            "reference": "DEP-************"
        },
        "instructions": [
            "Transfer exactly €500.00",
            "Use reference: DEP-************",
            "Processing time: 1-3 business days"
        ],
        "fees": {
            "deposit_fee": "0.00",
            "processing_fee": "0.00",
            "total_fee": "0.00"
        },
        "estimated_completion": "2024-01-04T14:00:00.000000Z",
        "expires_at": "2024-01-08T14:00:00.000000Z",
        "qr_code": {
            "image_url": "https://api.example.com/qr/deposit/DEP-************.png"
        },
        "created_at": "2024-01-01T14:00:00.000000Z",
        "updated_at": "2024-01-01T14:00:00.000000Z"
    }
}
```

**Error Responses:**

**422 Validation Error:**
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "amount": [
            "The amount must be at least 10.00",
            "The amount may not be greater than 50000.00"
        ],
        "currency": ["The selected currency is invalid"],
        "method": ["The selected method is invalid"]
    }
}
```

**400 Business Logic Error:**
```json
{
    "success": false,
    "message": "Daily deposit limit exceeded",
    "errors": {
        "limit": [
            "Daily limit: €10,000.00",
            "Already deposited today: €9,600.00",
            "Remaining limit: €400.00"
        ]
    }
}
```

---

### 4. Get Deposit Status

Para yatırma talebinin durumunu getirir.

**Endpoint:** `GET /api/wallet/deposit/{reference}`

**Authentication:** Bearer Token Required

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| reference | string | Yes | Para yatırma referans numarası |

**Example Request:**
```bash
curl -X GET "https://api.example.com/api/wallet/deposit/DEP-************" \
  -H "Authorization: Bearer your-token-here" \
  -H "Accept: application/json"
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Deposit status retrieved successfully",
    "data": {
        "id": 1,
        "reference": "DEP-************",
        "method": "bank_transfer",
        "currency": "EUR",
        "amount": "500.00",
        "status": "completed",
        "description": "Monthly deposit",
        "metadata": {
            "source": "salary",
            "category": "income"
        },
        "fees": {
            "deposit_fee": "0.00",
            "processing_fee": "0.00",
            "total_fee": "0.00"
        },
        "timeline": [
            {
                "status": "pending",
                "timestamp": "2024-01-01T14:00:00.000000Z",
                "description": "Deposit request created",
                "details": "Waiting for bank transfer"
            },
            {
                "status": "processing",
                "timestamp": "2024-01-02T10:30:00.000000Z",
                "description": "Bank transfer received",
                "details": "Processing deposit to wallet"
            },
            {
                "status": "completed",
                "timestamp": "2024-01-02T10:35:00.000000Z",
                "description": "Deposit completed successfully",
                "details": "Funds added to wallet balance"
            }
        ],
        "bank_details": {
            "sender_name": "John Doe",
            "sender_iban": "**********************",
            "bank_reference": "WIRE123456789",
            "received_amount": "500.00",
            "received_at": "2024-01-02T10:30:00.000000Z"
        },
        "transaction": {
            "id": 1,
            "reference": "TXN-********-001",
            "amount": "500.00",
            "status": "completed",
            "created_at": "2024-01-02T10:35:00.000000Z"
        },
        "created_at": "2024-01-01T14:00:00.000000Z",
        "updated_at": "2024-01-02T10:35:00.000000Z",
        "completed_at": "2024-01-02T10:35:00.000000Z"
    }
}
```

**Error Responses:**

**404 Not Found:**
```json
{
    "success": false,
    "message": "Deposit not found",
    "errors": {
        "reference": ["Deposit with reference DEP-************ not found"]
    }
}
```

---

### 5. Update Deposit

Para yatırma talebini günceller (sadece pending durumunda).

**Endpoint:** `PUT /api/wallet/deposit/{reference}`

**Authentication:** Bearer Token Required

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| reference | string | Yes | Para yatırma referans numarası |

**Request Body:**
```json
{
    "description": "Updated monthly deposit",
    "metadata": {
        "source": "bonus",
        "category": "income",
        "updated_reason": "Changed source type"
    }
}
```

**Request Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| description | string | No | Yeni açıklama |
| metadata | object | No | Güncellenmiş ek bilgiler |

**Example Request:**
```bash
curl -X PUT "https://api.example.com/api/wallet/deposit/DEP-************" \
  -H "Authorization: Bearer your-token-here" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "description": "Updated monthly deposit",
    "metadata": {
      "source": "bonus",
      "category": "income"
    }
  }'
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Deposit updated successfully",
    "data": {
        "id": 1,
        "reference": "DEP-************",
        "description": "Updated monthly deposit",
        "metadata": {
            "source": "bonus",
            "category": "income",
            "updated_reason": "Changed source type"
        },
        "updated_at": "2024-01-01T15:00:00.000000Z"
    }
}
```

**Error Responses:**

**400 Bad Request:**
```json
{
    "success": false,
    "message": "Cannot update deposit",
    "errors": {
        "status": ["Deposit can only be updated when status is pending"]
    }
}
```

---

### 6. Cancel Deposit

Bekleyen para yatırma talebini iptal eder.

**Endpoint:** `DELETE /api/wallet/deposit/{reference}`

**Authentication:** Bearer Token Required

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| reference | string | Yes | Para yatırma referans numarası |

**Request Body:**
```json
{
    "reason": "Changed my mind about the deposit"
}
```

**Request Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| reason | string | No | İptal nedeni |

**Example Request:**
```bash
curl -X DELETE "https://api.example.com/api/wallet/deposit/DEP-************" \
  -H "Authorization: Bearer your-token-here" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "reason": "Changed my mind about the deposit"
  }'
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Deposit cancelled successfully",
    "data": {
        "id": 1,
        "reference": "DEP-************",
        "status": "cancelled",
        "cancellation_reason": "Changed my mind about the deposit",
        "cancelled_at": "2024-01-01T15:30:00.000000Z",
        "refund_info": {
            "applicable": false,
            "reason": "No funds were received yet"
        }
    }
}
```

**Error Responses:**

**400 Bad Request:**
```json
{
    "success": false,
    "message": "Cannot cancel deposit",
    "errors": {
        "status": ["Deposit cannot be cancelled once processing has started"]
    }
}
```

---

### 7. Get Deposit History

Para yatırma geçmişini getirir.

**Endpoint:** `GET /api/wallet/deposit/history`

**Authentication:** Bearer Token Required

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| currency | string | No | Para birimi filtresi |
| method | string | No | Yöntem filtresi |
| status | string | No | Durum filtresi |
| date_from | string | No | Başlangıç tarihi (Y-m-d) |
| date_to | string | No | Bitiş tarihi (Y-m-d) |
| amount_min | decimal | No | Minimum tutar |
| amount_max | decimal | No | Maksimum tutar |
| per_page | integer | No | Sayfa başına kayıt (default: 15) |
| page | integer | No | Sayfa numarası |

**Example Request:**
```bash
curl -X GET "https://api.example.com/api/wallet/deposit/history?currency=EUR&status=completed&per_page=20" \
  -H "Authorization: Bearer your-token-here" \
  -H "Accept: application/json"
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Deposit history retrieved successfully",
    "data": {
        "current_page": 1,
        "data": [
            {
                "id": 1,
                "reference": "DEP-************",
                "method": "bank_transfer",
                "currency": "EUR",
                "amount": "500.00",
                "status": "completed",
                "description": "Monthly deposit",
                "created_at": "2024-01-01T14:00:00.000000Z",
                "completed_at": "2024-01-02T10:35:00.000000Z",
                "processing_time": "20:35:00"
            },
            {
                "id": 2,
                "reference": "DEP-************",
                "method": "qonto",
                "currency": "EUR",
                "amount": "1000.00",
                "status": "completed",
                "description": "Business deposit",
                "created_at": "2024-01-05T09:00:00.000000Z",
                "completed_at": "2024-01-05T09:15:00.000000Z",
                "processing_time": "00:15:00"
            }
        ],
        "summary": {
            "total_deposits": 2,
            "total_amount": "1500.00",
            "average_amount": "750.00",
            "average_processing_time": "10:25:00",
            "success_rate": "100%"
        },
        "first_page_url": "https://api.example.com/api/wallet/deposit/history?page=1",
        "from": 1,
        "last_page": 1,
        "per_page": 20,
        "total": 2
    }
}
```

---

### 8. Resend Deposit Instructions

Para yatırma talimatlarını yeniden gönderir.

**Endpoint:** `POST /api/wallet/deposit/{reference}/resend-instructions`

**Authentication:** Bearer Token Required

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| reference | string | Yes | Para yatırma referans numarası |

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "include_qr": true
}
```

**Request Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| email | string | No | E-posta adresi (varsayılan: hesap e-postası) |
| include_qr | boolean | No | QR kod dahil et (default: true) |

**Example Request:**
```bash
curl -X POST "https://api.example.com/api/wallet/deposit/DEP-************/resend-instructions" \
  -H "Authorization: Bearer your-token-here" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "email": "<EMAIL>",
    "include_qr": true
  }'
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Deposit instructions sent successfully",
    "data": {
        "reference": "DEP-************",
        "email": "<EMAIL>",
        "sent_at": "2024-01-01T16:00:00.000000Z",
        "includes_qr": true,
        "email_id": "email_123456"
    }
}
```

## 🔍 Deposit Status Flow

```
pending → processing → completed
   ↓           ↓
cancelled   failed
```

### Status Descriptions:
- **pending**: Talep oluşturuldu, ödeme bekleniyor
- **processing**: Ödeme alındı, işleniyor
- **completed**: İşlem tamamlandı, bakiye güncellendi
- **failed**: İşlem başarısız oldu
- **cancelled**: Talep iptal edildi

## 🔍 Common Error Codes

| Error Code | Description | Solution |
|------------|-------------|----------|
| DEPOSIT_001 | Invalid deposit method | Use supported methods: bank_transfer, qonto, paypal |
| DEPOSIT_002 | Amount below minimum | Check method minimum limits |
| DEPOSIT_003 | Amount above maximum | Check method maximum limits |
| DEPOSIT_004 | Daily limit exceeded | Wait for next day or use different method |
| DEPOSIT_005 | Monthly limit exceeded | Wait for next month or contact support |
| DEPOSIT_006 | Currency not supported | Use supported currencies for the method |
| DEPOSIT_007 | Method temporarily disabled | Try different method or wait |
| DEPOSIT_008 | Deposit already processed | Cannot modify completed deposits |
| DEPOSIT_009 | Deposit expired | Create new deposit request |
| DEPOSIT_010 | Invalid bank details | Verify sender information |

## 📊 Rate Limits

| Endpoint | Rate Limit | Window |
|----------|------------|---------|
| GET /methods | 30 requests | 1 minute |
| GET /accounts/* | 20 requests | 1 minute |
| POST /deposit | 5 requests | 1 minute |
| GET /deposit/* | 60 requests | 1 minute |
| PUT /deposit/* | 10 requests | 1 minute |
| DELETE /deposit/* | 3 requests | 1 minute |
| GET /history | 30 requests | 1 minute |
| POST /resend-instructions | 3 requests | 5 minutes |
