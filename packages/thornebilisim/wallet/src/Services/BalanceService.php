<?php

namespace Thorne\Wallet\Services;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Thorne\Wallet\Events\WalletBalanceSynced;
use Thorne\Wallet\Exceptions\Web3SyncException;
use Thorne\Wallet\Models\WalletBalance;

class BalanceService
{
    /**
     * Sync customer balance with WEB3 service.
     *
     * @param  int  $customerId
     * @param  string  $currency
     * @return WalletBalance
     */
    public function syncWithWeb3(int $customerId, string $currency): WalletBalance
    {
        if (! wallet_config('web3.enabled', true)) {
            throw new \RuntimeException('WEB3 integration is disabled');
        }

        $cacheKey = "wallet_balance_{$customerId}_{$currency}";

        try {
            // Get balance from WEB3 service
            $web3Data = $this->fetchWeb3Balance($customerId, $currency);

            // Get or create balance record
            $balance = WalletBalance::getOrCreateForCustomer($customerId, $currency);

            // Update balance with WEB3 data
            $balance->updateFromWeb3($web3Data);

            // Cache the result
            Cache::put($cacheKey, $balance, now()->addMinutes(wallet_config('web3.cache_ttl', 30)));

            if (wallet_config('logging.log_balance_checks', true)) {
                Log::channel(wallet_config('logging.channel', 'wallet'))
                    ->info('Balance synced with WEB3', [
                        'customer_id' => $customerId,
                        'currency' => $currency,
                        'balance' => $balance->balance,
                        'locked_balance' => $balance->locked_balance,
                    ]);
            }

            return $balance;

        } catch (\Exception $e) {
            Log::channel(wallet_config('logging.channel', 'wallet'))
                ->error('Failed to sync balance with WEB3', [
                    'customer_id' => $customerId,
                    'currency' => $currency,
                    'error' => $e->getMessage(),
                ]);

            // Return existing balance or create with zero balance
            return WalletBalance::getOrCreateForCustomer($customerId, $currency);
        }
    }

    /**
     * Get customer balance (with optional WEB3 sync).
     *
     * @param  int  $customerId
     * @param  string  $currency
     * @param  bool  $forceSync
     * @return WalletBalance
     */
    public function getBalance(int $customerId, string $currency, bool $forceSync = false): WalletBalance
    {
        $balance = WalletBalance::getCustomerBalance($customerId, $currency);

        if (! $balance) {
            $balance = WalletBalance::getOrCreateForCustomer($customerId, $currency);
        }

        // Check if sync is needed
        if ($forceSync || $balance->needsWeb3Sync()) {
            $balance = $this->syncWithWeb3($customerId, $currency);
        }

        return $balance;
    }

    /**
     * Get all customer balances.
     *
     * @param  int  $customerId
     * @param  bool  $forceSync
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllBalances(int $customerId, bool $forceSync = false)
    {
        $balances = WalletBalance::getCustomerBalances($customerId);

        if ($forceSync) {
            foreach ($balances as $balance) {
                $this->syncWithWeb3($customerId, $balance->currency);
            }

            // Refresh collection
            $balances = WalletBalance::getCustomerBalances($customerId);
        }

        return $balances;
    }

    /**
     * Check if customer has sufficient balance.
     *
     * @param  int  $customerId
     * @param  string  $currency
     * @param  string|float  $amount
     * @param  bool  $includeLockedBalance
     * @return bool
     */
    public function hasSufficientBalance(int $customerId, string $currency, string|float $amount, bool $includeLockedBalance = false): bool
    {
        $balance = $this->getBalance($customerId, $currency);

        return $balance->hasSufficientBalance($amount, $includeLockedBalance);
    }

    /**
     * Lock amount from customer balance.
     *
     * @param  int  $customerId
     * @param  string  $currency
     * @param  string|float  $amount
     * @return bool
     */
    public function lockAmount(int $customerId, string $currency, string|float $amount): bool
    {
        $balance = $this->getBalance($customerId, $currency);

        return $balance->lockAmount($amount);
    }

    /**
     * Unlock amount from customer balance.
     *
     * @param  int  $customerId
     * @param  string  $currency
     * @param  string|float  $amount
     * @return bool
     */
    public function unlockAmount(int $customerId, string $currency, string|float $amount): bool
    {
        $balance = $this->getBalance($customerId, $currency);

        return $balance->unlockAmount($amount);
    }

    /**
     * Compare database balance with WEB3 balance.
     *
     * @param  int  $customerId
     * @param  string  $currency
     * @return array
     */
    public function compareBalances(int $customerId, string $currency): array
    {
        $dbBalance = WalletBalance::getCustomerBalance($customerId, $currency);
        $dbAmount = $dbBalance ? $dbBalance->balance : '0.00000000';

        try {
            $web3Data = $this->fetchWeb3Balance($customerId, $currency);
            $web3Amount = $web3Data['balance'] ?? '0.00000000';

            $matches = bccomp($dbAmount, $web3Amount, 8) === 0;

            return [
                'matches' => $matches,
                'database_balance' => $dbAmount,
                'web3_balance' => $web3Amount,
                'difference' => bcsub($web3Amount, $dbAmount, 8),
                'last_sync' => $dbBalance?->last_web3_sync?->toISOString(),
            ];

        } catch (\Exception $e) {
            return [
                'matches' => null,
                'database_balance' => $dbAmount,
                'web3_balance' => null,
                'difference' => null,
                'error' => $e->getMessage(),
                'last_sync' => $dbBalance?->last_web3_sync?->toISOString(),
            ];
        }
    }

    /**
     * Fetch balance from WEB3 service.
     *
     * @param  int  $customerId
     * @param  string  $currency
     * @return array
     * @throws \Exception
     */
    protected function fetchWeb3Balance(int $customerId, string $currency): array
    {
        $baseUrl = wallet_config('web3.base_url');
        $timeout = wallet_config('web3.timeout', 30);
        $retryAttempts = wallet_config('web3.retry_attempts', 3);

        if (! $baseUrl) {
            throw new \RuntimeException('WEB3 base URL is not configured');
        }

        $payload = [
            'userId' => $customerId,
            'tenantId' => wallet_config('web3.tenant_id', 50),
            'currency' => $currency === 'MLGR' ? '' : $currency, // Empty for MLGR
            'chainId' => wallet_config('web3.chain_id', 'mirum-testnet'),
        ];

        $attempt = 1;
        $lastException = null;

        while ($attempt <= $retryAttempts) {
            try {
                $response = Http::timeout($timeout)
                    ->post("{$baseUrl}/wallet/balance", $payload);

                if ($response->successful()) {
                    $data = $response->json();

                    // Find balance for the specific currency
                    $balances = $data['balances'] ?? [];
                    $currencyBalance = null;

                    foreach ($balances as $balance) {
                        if ($balance['isStableCoin'] === true) {
                            $balanceCurrency = $balance['isoCurrency'] ?? '';

                            // Match currency
                            if (($currency === 'MLGR' && empty($balanceCurrency)) ||
                                ($currency !== 'MLGR' && $balanceCurrency === $currency)) {
                                $currencyBalance = $balance;
                                break;
                            }
                        }
                    }

                    if ($currencyBalance) {
                        // Convert from minor units to major units
                        $converter = app(\App\Services\CurrencyConverter::class);
                        $exponent = $currencyBalance['exponent'] ?? 2;
                        $balance = $converter->convertToMajorUnits(
                            (string) $currencyBalance['balance'],
                            $exponent
                        );

                        return [
                            'balance' => $balance,
                            'locked_balance' => '0.00000000', // WEB3 doesn't provide locked balance
                            'currency' => $currency,
                            'raw_response' => $data,
                            'synced_at' => now()->toISOString(),
                        ];
                    }

                    // Currency not found, return zero balance
                    return [
                        'balance' => '0.00000000',
                        'locked_balance' => '0.00000000',
                        'currency' => $currency,
                        'raw_response' => $data,
                        'synced_at' => now()->toISOString(),
                    ];
                }

                throw new \RuntimeException("WEB3 API error: {$response->status()} - {$response->body()}");

            } catch (\Exception $e) {
                $lastException = $e;

                if ($attempt < $retryAttempts) {
                    sleep(pow(2, $attempt - 1)); // Exponential backoff
                }

                $attempt++;
            }
        }

        throw new \RuntimeException("Failed to fetch WEB3 balance after {$retryAttempts} attempts: " . $lastException->getMessage());
    }
}
