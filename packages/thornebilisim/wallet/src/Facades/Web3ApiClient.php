<?php

namespace Thorne\Wallet\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static \Thorne\Wallet\Services\Web3ApiClient endpoint(string $path)
 * @method static \Thorne\Wallet\Services\Web3ApiClient method(string $method)
 * @method static \Thorne\Wallet\Services\Web3ApiClient timeout(int $timeout)
 * @method static \Thorne\Wallet\Services\Web3ApiClient retries(int $attempts)
 * @method static \Thorne\Wallet\Services\Web3ApiClient withHeaders(array $headers)
 * @method static \Thorne\Wallet\Services\Web3ApiClient withQuery(array $params)
 * @method static \Thorne\Wallet\Services\Web3ApiClient withoutLogging()
 * @method static \Illuminate\Http\Client\Response get(array $query = [])
 * @method static \Illuminate\Http\Client\Response post(array $data = [])
 * @method static \Illuminate\Http\Client\Response put(array $data = [])
 * @method static \Illuminate\Http\Client\Response delete(array $data = [])
 * @method static \Illuminate\Http\Client\Response send(array $data = [])
 * @method static \Thorne\Wallet\Services\Web3ApiClient make()
 */
class Web3ApiClient extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return \Thorne\Wallet\Services\Web3ApiClient::class;
    }
}
