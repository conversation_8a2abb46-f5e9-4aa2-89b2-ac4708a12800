<?php

namespace Thorne\Wallet\Examples;

use Thorne\Wallet\Services\Web3ApiClient;
use <PERSON>\Wallet\Facades\Web3ApiClient as Web3;

/**
 * Web3ApiClient usage examples.
 * 
 * This class demonstrates various ways to use the Web3ApiClient
 * for making authenticated requests to the Web3 service.
 */
class Web3ApiClientExamples
{
    /**
     * Example 1: Basic balance request using service class.
     */
    public function getBalanceUsingService(int $customerId): array
    {
        $payload = [
            'userId' => $customerId,
            'tenantId' => 50,
            'chainId' => 'mirum-testnet',
        ];

        $response = Web3ApiClient::make()
            ->endpoint('/wallet/balance')
            ->post($payload);

        return $response->json();
    }

    /**
     * Example 2: Balance request using facade.
     */
    public function getBalanceUsingFacade(int $customerId): array
    {
        $payload = [
            'userId' => $customerId,
            'tenantId' => 50,
            'chainId' => 'mirum-testnet',
        ];

        $response = Web3::endpoint('/wallet/balance')
            ->post($payload);

        return $response->json();
    }

    /**
     * Example 3: Custom timeout and retry configuration.
     */
    public function getBalanceWithCustomConfig(int $customerId): array
    {
        $payload = [
            'userId' => $customerId,
            'tenantId' => 50,
            'chainId' => 'mirum-testnet',
        ];

        $response = Web3ApiClient::make()
            ->endpoint('/wallet/balance')
            ->timeout(60) // 60 seconds timeout
            ->retries(5)  // 5 retry attempts
            ->post($payload);

        return $response->json();
    }

    /**
     * Example 4: GET request with query parameters.
     */
    public function getTransactionHistory(int $customerId, string $chainId): array
    {
        $response = Web3ApiClient::make()
            ->endpoint('/wallet/transactions')
            ->withQuery([
                'userId' => $customerId,
                'chainId' => $chainId,
                'limit' => 50,
            ])
            ->get();

        return $response->json();
    }

    /**
     * Example 5: Request with custom headers.
     */
    public function makeRequestWithCustomHeaders(array $data): array
    {
        $response = Web3ApiClient::make()
            ->endpoint('/wallet/custom-endpoint')
            ->withHeaders([
                'X-Custom-Header' => 'custom-value',
                'X-Request-ID' => uniqid(),
            ])
            ->post($data);

        return $response->json();
    }

    /**
     * Example 6: Silent request without logging.
     */
    public function makeSilentRequest(array $data): array
    {
        $response = Web3ApiClient::make()
            ->endpoint('/wallet/silent-endpoint')
            ->withoutLogging()
            ->post($data);

        return $response->json();
    }

    /**
     * Example 7: Different HTTP methods.
     */
    public function demonstrateHttpMethods(int $customerId): void
    {
        $client = Web3ApiClient::make();

        // GET request
        $getResponse = $client->endpoint('/wallet/info')->get(['userId' => $customerId]);

        // POST request
        $postResponse = $client->endpoint('/wallet/create')->post(['userId' => $customerId]);

        // PUT request
        $putResponse = $client->endpoint('/wallet/update')->put(['userId' => $customerId, 'status' => 'active']);

        // DELETE request
        $deleteResponse = $client->endpoint('/wallet/delete')->delete(['userId' => $customerId]);
    }

    /**
     * Example 8: Error handling.
     */
    public function handleErrors(int $customerId): array
    {
        try {
            $response = Web3ApiClient::make()
                ->endpoint('/wallet/balance')
                ->post([
                    'userId' => $customerId,
                    'tenantId' => 50,
                    'chainId' => 'mirum-testnet',
                ]);

            if ($response->successful()) {
                return $response->json();
            } else {
                throw new \RuntimeException("API request failed: {$response->status()}");
            }

        } catch (\Thorne\Wallet\Exceptions\Web3ApiException $e) {
            // Handle Web3 API specific errors
            \Log::error('Web3 API Error', [
                'message' => $e->getMessage(),
                'context' => $e->getContext(),
            ]);
            
            throw $e;
        } catch (\Exception $e) {
            // Handle general errors
            \Log::error('General Error', ['message' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Example 9: Dependency injection usage.
     */
    public function usingDependencyInjection(Web3ApiClient $web3Client, int $customerId): array
    {
        $payload = [
            'userId' => $customerId,
            'tenantId' => 50,
            'chainId' => 'mirum-testnet',
        ];

        $response = $web3Client
            ->endpoint('/wallet/balance')
            ->post($payload);

        return $response->json();
    }

    /**
     * Example 10: Chaining multiple configurations.
     */
    public function chainedConfiguration(int $customerId): array
    {
        $response = Web3ApiClient::make()
            ->endpoint('/wallet/balance')
            ->method('POST')
            ->timeout(45)
            ->retries(3)
            ->withHeaders(['X-Priority' => 'high'])
            ->withQuery(['version' => 'v2'])
            ->send([
                'userId' => $customerId,
                'tenantId' => 50,
                'chainId' => 'mirum-testnet',
            ]);

        return $response->json();
    }
}
