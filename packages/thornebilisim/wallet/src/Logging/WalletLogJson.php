<?php

namespace Thorne\Wallet\Logging;

use Monolog\Formatter\JsonFormatter;
use Monolog\LogRecord;

class WalletLog<PERSON>son
{
    public function __invoke($logger): void
    {
        foreach ($logger->getHandlers() as $handler) {
            $handler->setFormatter(new class extends JsonFormatter {
                public function __construct()
                {
                    parent::__construct(JsonFormatter::BATCH_MODE_NEWLINES, true);
                }

                public function format(array $record): string
                {
                    $timestamp = $record['datetime']->format('Y-m-d H:i:s');
                    $env       = config('app.env', 'production');
                    $level     = strtoupper($record['level_name']);
                    $message   = $record['message'];
                    $prefix    = "[{$timestamp}] {$env}.{$level}: {$message} ";

                    $data = [
                        'timestamp'      => $timestamp,
                        'level'          => $level,
                        'message'        => $message,
                        'context'        => $record['context'],
                        'extra'          => $record['extra'],
                        'channel'        => $record['channel'],
                        'service'        => 'wallet',
                    ];

                    if (isset($record['context']['customer_id'])) {
                        $data['customer_id'] = $record['context']['customer_id'];
                    }

                    if (isset($record['context']['currency'])) {
                        $data['currency'] = $record['context']['currency'];
                    }

                    if (isset($record['context']['transaction_id'])) {
                        $data['transaction_id'] = $record['context']['transaction_id'];
                    }

                    if (isset($record['context']['amount'])) {
                        $data['amount'] = $record['context']['amount'];
                    }

                    return $prefix . json_encode($data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . "\n";
                }
            });
        }
    }
}
