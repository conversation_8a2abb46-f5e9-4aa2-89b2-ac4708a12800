<?php

namespace Thorne\Wallet\Logging;

use Monolog\Formatter\JsonFormatter;
use Monolog\LogRecord;

class WalletLogJson
{
    /**
     * Customize the given logger instance.
     */
    public function __invoke($logger): void
    {
        foreach ($logger->getHandlers() as $handler) {
            $handler->setFormatter(new class extends JsonFormatter {
                public function format(LogRecord $record): string
                {
                    $data = [
                        'timestamp' => $record->datetime->format('Y-m-d H:i:s'),
                        'level' => $record->level->getName(),
                        'message' => $record->message,
                        'context' => $record->context,
                        'extra' => $record->extra,
                        'channel' => $record->channel,
                        'service' => 'wallet',
                        'version' => '1.0.0',
                    ];

                    // Add wallet-specific context
                    if (isset($record->context['customer_id'])) {
                        $data['customer_id'] = $record->context['customer_id'];
                    }

                    if (isset($record->context['currency'])) {
                        $data['currency'] = $record->context['currency'];
                    }

                    if (isset($record->context['transaction_id'])) {
                        $data['transaction_id'] = $record->context['transaction_id'];
                    }

                    if (isset($record->context['amount'])) {
                        $data['amount'] = $record->context['amount'];
                    }

                    return json_encode($data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . "\n";
                }
            });
        }
    }
}
