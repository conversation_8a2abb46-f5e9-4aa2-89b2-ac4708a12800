<?php

namespace Thorne\Wallet\Exceptions;

class WalletPermissionException extends WalletException
{
    protected array $context = [];

    public function __construct(string $message, int $code = 0, ?\Throwable $previous = null, array $context = [])
    {
        parent::__construct($message, $code, $previous);
        $this->context = $context;
    }

    public function getContext(): array
    {
        return $this->context;
    }

    public function setContext(array $context): self
    {
        $this->context = $context;
        return $this;
    }

    public function getOperation(): ?string
    {
        return $this->context['operation'] ?? null;
    }

    public function getCustomerId(): ?int
    {
        return $this->context['customer_id'] ?? null;
    }

    public function getAmount(): ?float
    {
        return $this->context['amount'] ?? null;
    }

    public function getCurrency(): ?string
    {
        return $this->context['currency'] ?? null;
    }

    public function getErrors(): array
    {
        return $this->context['errors'] ?? [];
    }
}
