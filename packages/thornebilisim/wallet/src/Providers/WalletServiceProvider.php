<?php

namespace Thorne\Wallet\Providers;

use Illuminate\Routing\Router;
use Illuminate\Support\ServiceProvider;
use <PERSON>\Wallet\Facades\LuhnService as LuhnServiceFacade;
use Thorne\Wallet\Facades\WalletService as WalletServiceFacade;
use <PERSON>\Wallet\Facades\Web3ApiClient as Web3ApiClientFacade;
use Thorne\Wallet\Services\BalanceService;
use Thorne\Wallet\Services\LuhnService;
use Thorne\Wallet\Services\TransferService;
use Thorne\Wallet\Services\WalletService;
use Thorne\Wallet\Services\Web3ApiClient;
use Thorne\Wallet\Console\Commands\SyncWalletBalancesCommand;
use Thorne\Wallet\Console\Commands\ProcessPendingTransfersCommand;
use Thorne\Wallet\Console\Commands\GenerateAccountNumberCommand;

class WalletServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->registerConfig();
        $this->registerServices();
        $this->registerFacades();
    }

    /**
     * Bootstrap services.
     */
    public function boot(Router $router): void
    {
        if (! wallet_config('enabled', true)) {
            return;
        }

        $this->bootPackageResources();
        $this->bootMiddleware($router);
        $this->bootCustomerRelations();
    }

    /**
     * Register package configuration.
     */
    protected function registerConfig(): void
    {
        $packageRoot = dirname(__DIR__, 2);

        $this->mergeConfigFrom($packageRoot . '/config/wallet.php', 'wallet');

        // Merge logging configuration
        $this->mergeConfigFrom($packageRoot . '/config/logging.php', 'logging.channels');
    }

    /**
     * Register package services.
     */
    protected function registerServices(): void
    {
        // Register Luhn Service
        $this->app->singleton(LuhnService::class, function () {
            return new LuhnService();
        });

        // Register Wallet Service
        $this->app->singleton(WalletService::class, function ($app) {
            return new WalletService(
                $app->make(LuhnService::class),
                $app->make(BalanceService::class),
                $app->make(TransferService::class)
            );
        });

        // Register Balance Service
        $this->app->singleton(BalanceService::class, function () {
            return new BalanceService();
        });

        // Register Transfer Service
        $this->app->singleton(TransferService::class, function ($app) {
            return new TransferService(
                $app->make(BalanceService::class)
            );
        });

        // Register Web3 API Client
        $this->app->singleton(Web3ApiClient::class, function () {
            return new Web3ApiClient();
        });
    }

    /**
     * Register package facades.
     */
    protected function registerFacades(): void
    {
        $this->app->alias(LuhnService::class, 'wallet.luhn');
        $this->app->alias(WalletService::class, 'wallet.service');
        $this->app->alias(Web3ApiClient::class, 'wallet.web3');
    }

    /**
     * Boot package resources.
     */
    protected function bootPackageResources(): void
    {
        $packageRoot = dirname(__DIR__, 2);

        // Load migrations
        $this->loadMigrationsFrom($packageRoot . '/database/migrations');

        // Load routes
        $this->loadRoutesFrom($packageRoot . '/routes/web.php');
        $this->loadRoutesFrom($packageRoot . '/routes/api.php');

        // Load views
        $this->loadViewsFrom($packageRoot . '/resources/views', 'wallet');

        // Load translations
        $this->loadTranslationsFrom($packageRoot . '/resources/lang', 'wallet');

        // Publish configuration and assets
        if ($this->app->runningInConsole()) {
            $this->publishes([
                $packageRoot . '/config/wallet.php' => config_path('wallet.php'),
            ], 'wallet-config');

            $this->publishes([
                $packageRoot . '/resources/views' => resource_path('views/vendor/wallet'),
            ], 'wallet-views');

            $this->publishes([
                $packageRoot . '/resources/lang' => resource_path('lang/vendor/wallet'),
            ], 'wallet-lang');

            $this->publishes([
                $packageRoot . '/database/migrations' => database_path('migrations'),
            ], 'wallet-migrations');

            // Register commands
            $this->commands([
                SyncWalletBalancesCommand::class,
                ProcessPendingTransfersCommand::class,
                GenerateAccountNumberCommand::class,
            ]);
        }
    }

    /**
     * Boot middleware.
     */
    protected function bootMiddleware(Router $router): void
    {
        // Register wallet-specific middleware if needed
        // $router->aliasMiddleware('wallet.auth', \Thorne\Wallet\Http\Middleware\WalletAuth::class);
    }

    /**
     * Boot customer model relations.
     */
    protected function bootCustomerRelations(): void
    {
        // Add wallet relations to Customer model
        if (class_exists(\Webkul\Customer\Models\Customer::class)) {
            \Webkul\Customer\Models\Customer::resolveRelationUsing('walletAccounts', function ($customerModel) {
                return $customerModel->hasMany(\Thorne\Wallet\Models\WalletAccount::class, 'customer_id');
            });

            \Webkul\Customer\Models\Customer::resolveRelationUsing('walletBalances', function ($customerModel) {
                return $customerModel->hasMany(\Thorne\Wallet\Models\WalletBalance::class, 'customer_id');
            });

            \Webkul\Customer\Models\Customer::resolveRelationUsing('walletTransactions', function ($customerModel) {
                return $customerModel->hasMany(\Thorne\Wallet\Models\WalletTransaction::class, 'customer_id');
            });

            \Webkul\Customer\Models\Customer::resolveRelationUsing('walletTransfersFrom', function ($customerModel) {
                return $customerModel->hasMany(\Thorne\Wallet\Models\WalletTransfer::class, 'from_customer_id');
            });

            \Webkul\Customer\Models\Customer::resolveRelationUsing('walletTransfersTo', function ($customerModel) {
                return $customerModel->hasMany(\Thorne\Wallet\Models\WalletTransfer::class, 'to_customer_id');
            });
        }
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            LuhnService::class,
            WalletService::class,
            BalanceService::class,
            TransferService::class,
            Web3ApiClient::class,
            'wallet.luhn',
            'wallet.service',
            'wallet.web3',
        ];
    }
}
