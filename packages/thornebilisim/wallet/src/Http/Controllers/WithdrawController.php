<?php

namespace Thorne\Wallet\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Thorne\Wallet\Services\WalletService;
use Thorne\Wallet\Services\BalanceService;
use Thorne\Wallet\Models\WalletTransaction;
use Thorne\Wallet\Enums\TransactionType;

class WithdrawController extends Controller
{
    public function __construct(
        protected WalletService $walletService,
        protected BalanceService $balanceService
    ) {
        $this->middleware(function ($request, $next) {
            if (! auth()->guard('customer')->check()) {
                return redirect()->route('customer.session.index');
            }
            return $next($request);
        });
    }

    /**
     * Display withdrawal page.
     */
    public function index(): View
    {
        $customer = auth()->guard('customer')->user();
        $methods = $this->walletService->getAvailablePaymentMethods($customer->id, 'withdraw');
        $currencies = wallet_get_enabled_currencies();
        $balances = $this->balanceService->getAllBalances($customer->id);
        
        return view('wallet::withdraw.index', compact('methods', 'currencies', 'balances'));
    }

    /**
     * Get available withdrawal methods.
     */
    public function methods(): JsonResponse
    {
        $customer = auth()->guard('customer')->user();
        $currency = request()->get('currency');
        
        $methods = $this->walletService->getAvailablePaymentMethods($customer->id, 'withdraw', $currency);
        
        return response()->json([
            'success' => true,
            'data' => $methods,
        ]);
    }

    /**
     * Show create withdrawal form.
     */
    public function create(): View
    {
        $customer = auth()->guard('customer')->user();
        $methods = $this->walletService->getAvailablePaymentMethods($customer->id, 'withdraw');
        $currencies = wallet_get_enabled_currencies();
        $balances = $this->balanceService->getAllBalances($customer->id);
        
        return view('wallet::withdraw.create', compact('methods', 'currencies', 'balances'));
    }

    /**
     * Store a new withdrawal request.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'method' => 'required|string',
            'currency' => 'required|string|size:3',
            'amount' => 'required|numeric|min:0.01',
            'account_details' => 'required|array',
            'description' => 'nullable|string|max:255',
        ]);

        $customer = auth()->guard('customer')->user();
        $currency = strtoupper($request->currency);

        // Check balance
        if (! $this->balanceService->hasSufficientBalance($customer->id, $currency, $request->amount)) {
            return redirect()
                ->back()
                ->withInput()
                ->with('error', __('wallet::app.withdrawals.insufficient_balance'));
        }

        try {
            $withdrawal = $this->walletService->createWithdrawal([
                'customer_id' => $customer->id,
                'method' => $request->method,
                'currency' => $currency,
                'amount' => $request->amount,
                'description' => $request->description,
                'metadata' => [
                    'account_details' => $request->account_details,
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ],
            ]);

            return redirect()
                ->route('customer.account.wallet.withdraw.show', $withdrawal->id)
                ->with('success', __('wallet::app.withdrawals.success'));

        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withInput()
                ->with('error', $e->getMessage());
        }
    }

    /**
     * Show withdrawal details.
     */
    public function show(WalletTransaction $transaction): View
    {
        $customer = auth()->guard('customer')->user();

        if ($transaction->customer_id !== $customer->id || $transaction->type !== TransactionType::WITHDRAWAL) {
            abort(404);
        }

        return view('wallet::withdraw.show', compact('transaction'));
    }
}
