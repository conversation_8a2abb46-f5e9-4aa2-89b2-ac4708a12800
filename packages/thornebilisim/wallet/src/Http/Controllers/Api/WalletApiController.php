<?php

namespace Thorne\Wallet\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Thorne\Wallet\Http\Controllers\Controller;
use Thorne\Wallet\Services\WalletService;
use Thorne\Wallet\Services\BalanceService;
use Thorne\Wallet\Services\LuhnService;
use Thorne\Wallet\Models\WalletAccount;
use Thorne\Wallet\Models\WalletTransaction;
use Webkul\Customer\Models\Customer;

class WalletApiController extends Controller
{
    public function __construct(
        protected WalletService $walletService,
        protected BalanceService $balanceService,
        protected LuhnService $luhnService
    ) {}

    /**
     * Get supported currencies.
     */
    public function currencies(): JsonResponse
    {
        $currencies = wallet_get_enabled_currencies();

        return response()->json([
            'success' => true,
            'data' => $currencies,
        ]);
    }

    /**
     * Get available payment methods.
     */
    public function paymentMethods(): JsonResponse
    {
        $operation = request()->get('operation'); // deposit, withdraw
        $currency = request()->get('currency');

        if (! $operation) {
            return response()->json([
                'success' => false,
                'message' => 'Operation parameter is required (deposit or withdraw)',
            ], 400);
        }

        $methods = wallet_config('payment_methods', []);
        $available = [];

        foreach ($methods as $methodKey => $methodConfig) {
            if (! $methodConfig['enabled']) {
                continue;
            }

            $canPerformOperation = match ($operation) {
                'deposit' => $methodConfig['can_deposit'],
                'withdraw' => $methodConfig['can_withdraw'],
                default => false,
            };

            if (! $canPerformOperation) {
                continue;
            }

            if ($currency && ! in_array($currency, $methodConfig['supported_currencies'])) {
                continue;
            }

            $available[$methodKey] = $methodConfig;
        }

        return response()->json([
            'success' => true,
            'data' => $available,
        ]);
    }

    /**
     * Validate account number.
     */
    public function validateAccountNumber(Request $request): JsonResponse
    {
        $request->validate([
            'account_number' => 'required|string|size:11',
        ]);

        $accountNumber = $request->account_number;

        // Validate Luhn algorithm
        if (! $this->luhnService->validateAccountNumber($accountNumber)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid account number format',
                'data' => [
                    'is_valid' => false,
                    'reason' => 'luhn_validation_failed',
                ],
            ]);
        }

        // Check if account exists
        $account = WalletAccount::findByAccountNumber($accountNumber);
        if (! $account) {
            return response()->json([
                'success' => false,
                'message' => 'Account not found',
                'data' => [
                    'is_valid' => false,
                    'reason' => 'account_not_found',
                ],
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Account is valid',
            'data' => [
                'is_valid' => true,
                'account_number' => $account->account_number,
                'formatted_account_number' => $account->getFormattedAccountNumber(),
                'currency' => $account->currency,
                'is_active' => $account->is_active,
            ],
        ]);
    }

    /**
     * Get wallet overview (authenticated).
     */
    public function overview(): JsonResponse
    {
        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));
        $syncWithWeb3 = request()->boolean('sync', false);

        $overview = $this->walletService->getWalletOverview($customer, $syncWithWeb3);

        return response()->json([
            'success' => true,
            'data' => $overview,
        ]);
    }

    /**
     * Get customer balance (authenticated).
     */
    public function balance(): JsonResponse
    {
        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));
        $currency = request()->get('currency');
        $forceSync = request()->boolean('sync', false);

        if ($currency) {
            if (! wallet_is_supported_currency($currency)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unsupported currency',
                ], 400);
            }

            $balance = $this->balanceService->getBalance($customer->id, $currency, $forceSync);

            return response()->json([
                'success' => true,
                'data' => [
                    'currency' => $balance->currency,
                    'balance' => $balance->balance,
                    'locked_balance' => $balance->locked_balance,
                    'available_balance' => $balance->getAvailableBalance(),
                    'formatted_balance' => $balance->getFormattedBalance(),
                    'formatted_available_balance' => $balance->getFormattedAvailableBalance(),
                    'last_sync' => $balance->last_web3_sync?->toISOString(),
                ],
            ]);
        }

        $balances = $this->balanceService->getAllBalances($customer->id, $forceSync);

        return response()->json([
            'success' => true,
            'data' => $balances->map(function ($balance) {
                return [
                    'currency' => $balance->currency,
                    'balance' => $balance->balance,
                    'locked_balance' => $balance->locked_balance,
                    'available_balance' => $balance->getAvailableBalance(),
                    'formatted_balance' => $balance->getFormattedBalance(),
                    'formatted_available_balance' => $balance->getFormattedAvailableBalance(),
                    'last_sync' => $balance->last_web3_sync?->toISOString(),
                ];
            }),
        ]);
    }

    /**
     * Sync balance with WEB3 service (authenticated).
     */
    public function syncBalance(Request $request): JsonResponse
    {
        $request->validate([
            'currency' => 'required|string',
        ]);

        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));
        $currency = strtoupper($request->currency);

        if (! wallet_is_supported_currency($currency)) {
            return response()->json([
                'success' => false,
                'message' => 'Unsupported currency',
            ], 400);
        }

        try {
            $balance = $this->balanceService->syncWithWeb3($customer->id, $currency);

            return response()->json([
                'success' => true,
                'data' => [
                    'currency' => $balance->currency,
                    'balance' => $balance->balance,
                    'locked_balance' => $balance->locked_balance,
                    'available_balance' => $balance->getAvailableBalance(),
                    'formatted_balance' => $balance->getFormattedBalance(),
                    'formatted_available_balance' => $balance->getFormattedAvailableBalance(),
                    'last_sync' => $balance->last_web3_sync?->toISOString(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to sync balance: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Compare database balance with WEB3 balance (authenticated).
     */
    public function compareBalance(): JsonResponse
    {
        $currency = request()->get('currency');

        if (! $currency) {
            return response()->json([
                'success' => false,
                'message' => 'Currency parameter is required',
            ], 400);
        }

        if (! wallet_is_supported_currency($currency)) {
            return response()->json([
                'success' => false,
                'message' => 'Unsupported currency',
            ], 400);
        }

        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));

        try {
            $comparison = $this->balanceService->compareBalances($customer->id, $currency);

            return response()->json([
                'success' => true,
                'data' => $comparison,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to compare balances: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get customer accounts (authenticated).
     */
    public function accounts(): JsonResponse
    {
        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));
        $accounts = $customer->walletAccounts()->active()->get();

        return response()->json([
            'success' => true,
            'data' => $accounts->map(function ($account) {
                return [
                    'id' => $account->id,
                    'currency' => $account->currency,
                    'account_number' => $account->account_number,
                    'formatted_account_number' => $account->getFormattedAccountNumber(),
                    'is_active' => $account->is_active,
                    'created_at' => $account->created_at->toISOString(),
                ];
            }),
        ]);
    }

    /**
     * Create a new account (authenticated).
     */
    public function createAccount(Request $request): JsonResponse
    {
        $request->validate([
            'currency' => 'required|string|in:' . implode(',', array_keys(wallet_get_enabled_currencies())),
        ]);

        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));
        $currency = strtoupper($request->currency);

        if (! wallet_is_supported_currency($currency)) {
            return response()->json([
                'success' => false,
                'message' => 'Unsupported currency',
            ], 400);
        }

        // Check if account already exists
        $existingAccount = WalletAccount::where('customer_id', $customer->id)
            ->where('currency', $currency)
            ->first();

        if ($existingAccount) {
            return response()->json([
                'success' => false,
                'message' => 'Account already exists for this currency',
            ], 400);
        }

        try {
            $account = WalletAccount::getOrCreateForCustomer($customer->id, $currency);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $account->id,
                    'currency' => $account->currency,
                    'account_number' => $account->account_number,
                    'formatted_account_number' => $account->getFormattedAccountNumber(),
                    'is_active' => $account->is_active,
                    'created_at' => $account->created_at->toISOString(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create account: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get transaction history (authenticated).
     */
    public function transactions(): JsonResponse
    {
        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));

        $filters = [
            'currency' => request()->get('currency'),
            'type' => request()->get('type'),
            'status' => request()->get('status'),
            'method' => request()->get('method'),
            'from_date' => request()->get('from_date'),
            'to_date' => request()->get('to_date'),
            'per_page' => request()->get('per_page', 15),
        ];

        $transactions = $this->walletService->getTransactionHistory($customer->id, $filters);

        return response()->json([
            'success' => true,
            'data' => $transactions->items(),
            'pagination' => [
                'current_page' => $transactions->currentPage(),
                'last_page' => $transactions->lastPage(),
                'per_page' => $transactions->perPage(),
                'total' => $transactions->total(),
                'from' => $transactions->firstItem(),
                'to' => $transactions->lastItem(),
            ],
        ]);
    }

    /**
     * Show a specific transaction (authenticated).
     */
    public function showTransaction(WalletTransaction $transaction): JsonResponse
    {
        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));

        if ($transaction->customer_id !== $customer->id) {
            return response()->json([
                'success' => false,
                'message' => 'Transaction not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $transaction->id,
                'reference' => $transaction->reference,
                'type' => $transaction->type->value,
                'direction' => $transaction->direction->value,
                'status' => $transaction->status->value,
                'method' => $transaction->method,
                'currency' => $transaction->currency,
                'amount' => $transaction->amount,
                'fee' => $transaction->fee,
                'net_amount' => $transaction->net_amount,
                'formatted_amount' => $transaction->getFormattedAmount(),
                'formatted_fee' => $transaction->getFormattedFee(),
                'formatted_net_amount' => $transaction->getFormattedNetAmount(),
                'description' => $transaction->description,
                'metadata' => $transaction->metadata,
                'external_reference' => $transaction->external_reference,
                'external_transaction_id' => $transaction->external_transaction_id,
                'processed_at' => $transaction->processed_at?->toISOString(),
                'failed_at' => $transaction->failed_at?->toISOString(),
                'failure_reason' => $transaction->failure_reason,
                'created_at' => $transaction->created_at->toISOString(),
                'updated_at' => $transaction->updated_at->toISOString(),
                'age' => $transaction->getAge(),
            ],
        ]);
    }
}
