<?php

namespace Thorne\Wallet\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Thorne\Wallet\Http\Controllers\Controller;
use Thorne\Wallet\Services\WalletService;
use Thorne\Wallet\Services\CompanyAccountService;
use Thorne\Wallet\Services\PermissionService;
use Thorne\Wallet\Models\WalletTransaction;
use Thorne\Wallet\Enums\TransactionType;
use Thorne\Wallet\Exceptions\WalletPermissionException;
use Webkul\Customer\Models\Customer;

class DepositApiController extends Controller
{
    public function __construct(
        protected WalletService $walletService,
        protected CompanyAccountService $companyAccountService,
        protected PermissionService $permissionService
    ) {
        $this->middleware(function ($request, $next) {
            if (! auth()->guard('customer')->check() && ! request()->secureContext('customer_id')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthenticated',
                ], 401);
            }
            return $next($request);
        });
    }

    /**
     * Get customer deposits.
     */
    public function index(): JsonResponse
    {
        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));

        $filters = [
            'currency' => request()->get('currency'),
            'status' => request()->get('status'),
            'method' => request()->get('method'),
            'from_date' => request()->get('from_date'),
            'to_date' => request()->get('to_date'),
            'per_page' => request()->get('per_page', 15),
        ];

        // Add type filter for deposits only
        $filters['type'] = TransactionType::DEPOSIT;

        $deposits = $this->walletService->getTransactionHistory($customer->id, $filters);

        return response()->json([
            'success' => true,
            'data' => $deposits->items(),
            'pagination' => [
                'current_page' => $deposits->currentPage(),
                'last_page' => $deposits->lastPage(),
                'per_page' => $deposits->perPage(),
                'total' => $deposits->total(),
                'from' => $deposits->firstItem(),
                'to' => $deposits->lastItem(),
            ],
        ]);
    }

    /**
     * Create a new deposit.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'method' => 'required|string',
            'currency' => 'required|string|in:EUR,USD,MLGR',
            'amount' => 'required|numeric|min:0.01',
            'description' => 'nullable|string|max:255',
            'account_id' => 'nullable|string', // Optional specific account
        ]);

        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));
        $currency = strtoupper($request->currency);

        try {
            // Check permissions
            $this->permissionService->validateOperationOrFail(
                'deposit',
                $customer->id,
                $request->amount,
                $currency
            );

            // Get company account for deposit
            $account = null;
            if ($request->account_id) {
                $account = $this->companyAccountService->getAccountById($request->account_id);
                if (!$account) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid account ID',
                    ], 400);
                }
            } else {
                $account = $this->companyAccountService->getAccountForDeposit(
                    $request->input('method'),
                    $currency,
                    $request->amount
                );
            }

            if (!$account) {
                return response()->json([
                    'success' => false,
                    'message' => 'No available account for this deposit method and currency',
                ], 400);
            }

            $deposit = $this->walletService->createDeposit([
                'customer_id' => $customer->id,
                'method' => $request->input('method'),
                'currency' => $currency,
                'amount' => $request->amount,
                'description' => $request->description,
                'account_info' => $account,
                'metadata' => [
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'account_id' => $account['id'],
                    'account_name' => $account['name'],
                ],
            ]);

            // Log permission check
            $this->permissionService->logPermissionCheck(
                'deposit',
                $customer->id,
                $request->amount,
                $currency,
                true
            );

            return response()->json([
                'success' => true,
                'message' => 'Deposit request created successfully',
                'data' => [
                    'id' => $deposit->id,
                    'reference' => $deposit->reference,
                    'type' => $deposit->type->value,
                    'status' => $deposit->status->value,
                    'method' => $deposit->method,
                    'currency' => $deposit->currency,
                    'amount' => $deposit->amount,
                    'formatted_amount' => $deposit->getFormattedAmount(),
                    'description' => $deposit->description,
                    'created_at' => $deposit->created_at->toISOString(),
                    'account_info' => $account,
                ],
            ]);

        } catch (WalletPermissionException $e) {
            // Log permission denial
            $this->permissionService->logPermissionCheck(
                'deposit',
                $customer->id,
                $request->amount,
                $currency,
                false,
                $e->getErrors()
            );

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'errors' => $e->getErrors(),
                'context' => $e->getContext(),
            ], 403);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Get available deposit methods.
     */
    public function methods(): JsonResponse
    {
        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));
        $currency = request()->get('currency');

        $methods = $this->walletService->getAvailablePaymentMethods($customer->id, 'deposit', $currency);

        return response()->json([
            'success' => true,
            'data' => $methods,
        ]);
    }

    /**
     * Get company accounts for deposits.
     */
    public function companyAccounts(): JsonResponse
    {
        $method = request()->get('method');
        $currency = request()->get('currency');
        $accountId = request()->get('account_id');

        try {
            if ($accountId) {
                $account = $this->companyAccountService->getAccountById($accountId);
                $accounts = $account ? [$account] : [];
            } elseif ($method && $currency) {
                $accounts = $this->companyAccountService->getAccountsByMethodAndCurrency($method, $currency);
            } elseif ($method) {
                $accounts = $this->companyAccountService->getActiveAccountsByMethod($method);
            } else {
                $accounts = $this->companyAccountService->getAllAccounts();
            }

            return response()->json([
                'success' => true,
                'data' => $accounts,
                'meta' => [
                    'total_accounts' => count($accounts),
                    'method' => $method,
                    'currency' => $currency,
                    'account_id' => $accountId,
                    'supported_currencies' => $method ? $this->companyAccountService->getSupportedCurrencies($method) : [],
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve company accounts: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Show deposit details.
     */
    public function show(WalletTransaction $transaction): JsonResponse
    {
        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));

        if ($transaction->customer_id !== $customer->id || $transaction->type !== TransactionType::DEPOSIT) {
            return response()->json([
                'success' => false,
                'message' => 'Deposit not found',
            ], 404);
        }

        // Get company accounts for this deposit
        $companyAccounts = wallet_config('company_accounts', []);
        $accounts = $companyAccounts[$transaction->method][$transaction->currency] ?? [];

        return response()->json([
            'success' => true,
            'data' => [
                'transaction' => [
                    'id' => $transaction->id,
                    'reference' => $transaction->reference,
                    'type' => $transaction->type->value,
                    'status' => $transaction->status->value,
                    'method' => $transaction->method,
                    'currency' => $transaction->currency,
                    'amount' => $transaction->amount,
                    'fee' => $transaction->fee,
                    'net_amount' => $transaction->net_amount,
                    'formatted_amount' => $transaction->getFormattedAmount(),
                    'formatted_fee' => $transaction->getFormattedFee(),
                    'formatted_net_amount' => $transaction->getFormattedNetAmount(),
                    'description' => $transaction->description,
                    'metadata' => $transaction->metadata,
                    'external_reference' => $transaction->external_reference,
                    'external_transaction_id' => $transaction->external_transaction_id,
                    'processed_at' => $transaction->processed_at?->toISOString(),
                    'failed_at' => $transaction->failed_at?->toISOString(),
                    'failure_reason' => $transaction->failure_reason,
                    'created_at' => $transaction->created_at->toISOString(),
                    'updated_at' => $transaction->updated_at->toISOString(),
                    'age' => $transaction->getAge(),
                ],
                'company_accounts' => $accounts,
            ],
        ]);
    }

    /**
     * Get account by ID.
     */
    public function getAccountById(string $accountId): JsonResponse
    {
        try {
            $account = $this->companyAccountService->getAccountById($accountId);

            if (!$account) {
                return response()->json([
                    'success' => false,
                    'message' => 'Account not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $account,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve account: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get accounts by method.
     */
    public function getAccountsByMethod(string $method): JsonResponse
    {
        try {
            $accounts = $this->companyAccountService->getActiveAccountsByMethod($method);
            $groupedAccounts = $this->companyAccountService->getAccountsGroupedByCurrency($method);

            return response()->json([
                'success' => true,
                'data' => $accounts,
                'grouped_by_currency' => $groupedAccounts,
                'meta' => [
                    'method' => $method,
                    'total_accounts' => count($accounts),
                    'supported_currencies' => $this->companyAccountService->getSupportedCurrencies($method),
                    'statistics' => $this->companyAccountService->getAccountStatistics(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve accounts: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get accounts by method and currency.
     */
    public function getAccountsByMethodAndCurrency(string $method, string $currency): JsonResponse
    {
        try {
            $accounts = $this->companyAccountService->getAccountsByMethodAndCurrency($method, $currency);
            $primaryAccount = $this->companyAccountService->getPrimaryAccount($method, $currency);

            return response()->json([
                'success' => true,
                'data' => $accounts,
                'primary_account' => $primaryAccount,
                'meta' => [
                    'method' => $method,
                    'currency' => $currency,
                    'total_accounts' => count($accounts),
                    'has_primary' => $primaryAccount !== null,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve accounts: ' . $e->getMessage(),
            ], 500);
        }
    }


}
