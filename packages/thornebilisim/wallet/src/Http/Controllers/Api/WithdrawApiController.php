<?php

namespace Thorne\Wallet\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Thorne\Wallet\Http\Controllers\Controller;
use Thorne\Wallet\Services\WalletService;
use Thorne\Wallet\Services\BalanceService;
use Thorne\Wallet\Models\WalletTransaction;
use Thorne\Wallet\Enums\TransactionType;
use Webkul\Customer\Models\Customer;

class WithdrawApiController extends Controller
{
    public function __construct(
        protected WalletService $walletService,
        protected BalanceService $balanceService
    ) {
        $this->middleware(function ($request, $next) {
            if (! auth()->guard('customer')->check() && ! request()->secureContext('customer_id')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthenticated',
                ], 401);
            }
            return $next($request);
        });
    }

    /**
     * Get customer withdrawals.
     */
    public function index(): JsonResponse
    {
        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));

        $filters = [
            'currency' => request()->get('currency'),
            'status' => request()->get('status'),
            'method' => request()->get('method'),
            'from_date' => request()->get('from_date'),
            'to_date' => request()->get('to_date'),
            'per_page' => request()->get('per_page', 15),
        ];

        // Add type filter for withdrawals only
        $filters['type'] = TransactionType::WITHDRAWAL;

        $withdrawals = $this->walletService->getTransactionHistory($customer->id, $filters);

        return response()->json([
            'success' => true,
            'data' => $withdrawals->items(),
            'pagination' => [
                'current_page' => $withdrawals->currentPage(),
                'last_page' => $withdrawals->lastPage(),
                'per_page' => $withdrawals->perPage(),
                'total' => $withdrawals->total(),
                'from' => $withdrawals->firstItem(),
                'to' => $withdrawals->lastItem(),
            ],
        ]);
    }

    /**
     * Create a new withdrawal.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'method' => 'required|string',
            'currency' => 'required|string',
            'amount' => 'required|numeric|min:0.01',
            'account_details' => 'required|array',
            'description' => 'nullable|string|max:255',
        ]);

        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));
        $currency = strtoupper($request->currency);

        // Check balance
        if (! $this->balanceService->hasSufficientBalance($customer->id, $currency, $request->amount)) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient balance',
            ], 400);
        }

        try {
            $withdrawal = $this->walletService->createWithdrawal([
                'customer_id' => $customer->id,
                'method' => $request->input('method'),
                'currency' => $currency,
                'amount' => $request->amount,
                'description' => $request->description,
                'metadata' => [
                    'account_details' => $request->account_details,
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ],
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Withdrawal request created successfully',
                'data' => [
                    'id' => $withdrawal->id,
                    'reference' => $withdrawal->reference,
                    'type' => $withdrawal->type->value,
                    'status' => $withdrawal->status->value,
                    'method' => $withdrawal->method,
                    'currency' => $withdrawal->currency,
                    'amount' => $withdrawal->amount,
                    'formatted_amount' => $withdrawal->getFormattedAmount(),
                    'description' => $withdrawal->description,
                    'created_at' => $withdrawal->created_at->toISOString(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Get available withdrawal methods.
     */
    public function methods(): JsonResponse
    {
        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));
        $currency = request()->get('currency');

        $methods = $this->walletService->getAvailablePaymentMethods($customer->id, 'withdraw', $currency);

        return response()->json([
            'success' => true,
            'data' => $methods,
        ]);
    }

    /**
     * Show withdrawal details.
     */
    public function show(WalletTransaction $transaction): JsonResponse
    {
        $customer = auth()->guard('customer')->user() ?? Customer::find(request()->secureContext('customer_id'));

        if ($transaction->customer_id !== $customer->id || $transaction->type !== TransactionType::WITHDRAWAL) {
            return response()->json([
                'success' => false,
                'message' => 'Withdrawal not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $transaction->id,
                'reference' => $transaction->reference,
                'type' => $transaction->type->value,
                'status' => $transaction->status->value,
                'method' => $transaction->method,
                'currency' => $transaction->currency,
                'amount' => $transaction->amount,
                'fee' => $transaction->fee,
                'net_amount' => $transaction->net_amount,
                'formatted_amount' => $transaction->getFormattedAmount(),
                'formatted_fee' => $transaction->getFormattedFee(),
                'formatted_net_amount' => $transaction->getFormattedNetAmount(),
                'description' => $transaction->description,
                'metadata' => $transaction->metadata,
                'external_reference' => $transaction->external_reference,
                'external_transaction_id' => $transaction->external_transaction_id,
                'processed_at' => $transaction->processed_at?->toISOString(),
                'failed_at' => $transaction->failed_at?->toISOString(),
                'failure_reason' => $transaction->failure_reason,
                'created_at' => $transaction->created_at->toISOString(),
                'updated_at' => $transaction->updated_at->toISOString(),
                'age' => $transaction->getAge(),
            ],
        ]);
    }
}
