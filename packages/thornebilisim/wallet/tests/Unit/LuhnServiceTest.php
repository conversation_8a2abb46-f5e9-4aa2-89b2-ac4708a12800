<?php

namespace Thorne\Wallet\Tests\Unit;

use PHPUnit\Framework\TestCase;
use Thorne\Wallet\Services\LuhnService;

class LuhnServiceTest extends TestCase
{
    protected LuhnService $luhnService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->luhnService = new LuhnService();
    }

    public function test_generate_check_digit()
    {
        // Test known Luhn check digits
        $this->assertEquals(0, $this->luhnService->generateCheckDigit('123456789'));
        $this->assertEquals(5, $this->luhnService->generateCheckDigit('12345'));
        $this->assertEquals(3, $this->luhnService->generateCheckDigit('4532015112830366'));
    }

    public function test_validate_luhn_number()
    {
        // Valid <PERSON>hn numbers
        $this->assertTrue($this->luhnService->validate('1234567890'));
        $this->assertTrue($this->luhnService->validate('123455'));
        $this->assertTrue($this->luhnService->validate('45320151128303663'));

        // Invalid Luhn numbers
        $this->assertFalse($this->luhnService->validate('1234567891'));
        $this->assertFalse($this->luhnService->validate('123456'));
        $this->assertFalse($this->luhnService->validate('45320151128303664'));
    }

    public function test_generate_with_check_digit()
    {
        $baseNumber = '12345678';
        $numberWithCheck = $this->luhnService->generateWithCheckDigit($baseNumber);
        
        $this->assertEquals(9, strlen($numberWithCheck));
        $this->assertTrue($this->luhnService->validate($numberWithCheck));
        $this->assertEquals($baseNumber, substr($numberWithCheck, 0, -1));
    }

    public function test_generate_random_number()
    {
        $length = 7;
        $randomNumber = $this->luhnService->generateRandomNumber($length);
        
        $this->assertEquals($length, strlen($randomNumber));
        $this->assertTrue(ctype_digit($randomNumber));
        $this->assertNotEquals('0', $randomNumber[0]); // First digit should not be 0
    }

    public function test_generate_account_number()
    {
        $currencyCode = '978'; // EUR
        $accountNumber = $this->luhnService->generateAccountNumber($currencyCode);
        
        $this->assertEquals(11, strlen($accountNumber));
        $this->assertTrue(ctype_digit($accountNumber));
        $this->assertTrue($this->luhnService->validateAccountNumber($accountNumber));
        $this->assertEquals($currencyCode, substr($accountNumber, 0, 3));
    }

    public function test_validate_account_number()
    {
        $currencyCode = '978';
        $accountNumber = $this->luhnService->generateAccountNumber($currencyCode);
        
        $this->assertTrue($this->luhnService->validateAccountNumber($accountNumber));
        $this->assertTrue($this->luhnService->validateAccountNumber($accountNumber, $currencyCode));
        $this->assertFalse($this->luhnService->validateAccountNumber($accountNumber, '840')); // Different currency
    }

    public function test_extract_currency_code()
    {
        $accountNumber = '***********';
        $this->assertEquals('978', $this->luhnService->extractCurrencyCode($accountNumber));
        
        $this->assertNull($this->luhnService->extractCurrencyCode('123')); // Too short
        $this->assertNull($this->luhnService->extractCurrencyCode('abc1234567')); // Non-numeric
    }

    public function test_extract_check_digit()
    {
        $accountNumber = '***********';
        $this->assertEquals(4, $this->luhnService->extractCheckDigit($accountNumber));
        
        $this->assertNull($this->luhnService->extractCheckDigit('123')); // Too short
    }

    public function test_extract_random_part()
    {
        $accountNumber = '***********';
        $this->assertEquals('1234567', $this->luhnService->extractRandomPart($accountNumber));
        
        $this->assertNull($this->luhnService->extractRandomPart('123')); // Too short
    }

    public function test_invalid_currency_code_throws_exception()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->luhnService->generateAccountNumber('12'); // Too short
    }

    public function test_non_numeric_currency_code_throws_exception()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->luhnService->generateAccountNumber('ABC'); // Non-numeric
    }

    public function test_invalid_random_length_throws_exception()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->luhnService->generateAccountNumber('978', 0); // Zero length
    }
}
