<?php

namespace Thorne\SecurityLayer\Exceptions;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class SecurityLayerExceptionHandler
{
    protected array $messages = [
        'https' => [
            'message' => 'Secure HTTPS connection required',
            'status'  => 301,
        ],
        'rate_limit' => [
            'message' => 'Rate limit exceeded',
            'status'  => 429,
        ],
        'signature' => [
            'missing' => [
                'message' => 'Request signature required',
                'status'  => 401,
            ],
            'invalid' => [
                'message' => 'Invalid request signature',
                'status'  => 401,
            ],
            'config_error' => [
                'message' => 'Signature validation configuration error',
                'status'  => 500,
            ],
            'json_error' => [
                'message' => 'Invalid request data format',
                'status'  => 400,
            ],
        ],
        'cors' => [
            'origin' => [
                'message' => 'Unauthorized request origin',
                'status'  => 403,
            ],
            'headers' => [
                'message' => 'Missing required security headers',
                'status'  => 403,
            ],
        ],
        'idempotency' => [
            'missing' => [
                'message' => 'Idempotency key required for request',
                'status'  => 400,
            ],
            'in_progress' => [
                'message' => 'Duplicate request in progress',
                'status'  => 429,
            ],
        ],
        'timestamp' => [
            'missing' => [
                'message' => 'Request timestamp required',
                'status'  => 400,
            ],
            'invalid' => [
                'message' => 'Request timestamp expired',
                'status'  => 400,
            ],
        ],
        'nonce' => [
            'missing' => [
                'message' => 'Request nonce and customer ID required',
                'status'  => 400,
            ],
            'duplicate' => [
                'message' => 'Nonce already processed',
                'status'  => 409,
            ],
            'replay_attack' => [
                'message' => 'Potential replay attack detected',
                'status'  => 409,
            ],
        ],
        'session' => [
            'missing' => [
                'message' => 'Customer ID required',
                'status'  => 401,
            ],
            'missing_token' => [
                'message' => 'Session token required',
                'status'  => 401,
            ],
            'invalid' => [
                'message' => 'Invalid session token',
                'status'  => 401,
            ],
            'invalid_format' => [
                'message' => 'Invalid session token format',
                'status'  => 401,
            ],
            'mismatch' => [
                'message' => 'Session customer ID mismatch',
                'status'  => 401,
            ],
        ],
        'config' => [
            'disabled' => [
                'message' => 'Security layer disabled, check configuration',
                'status'  => 503,
            ],
            'missing_signature_secret' => [
                'message' => 'Security layer signature secret not configured',
                'status'  => 500,
            ],
        ],
    ];

    public function handle(Request $request, string $type, ?string $subType = null, array $additionalData = []): Response|JsonResponse
    {
        $error = $this->getErrorMessage($type, $subType);

        if ($this->isApiRequest($request)) {
            return response()->json([
                'message' => $error['message'] ?? 'An error occurred',
                'status'  => $error['status']  ?? 500,
                ...$additionalData,
            ], $error['status'] ?? 500);
        }

        return response($error['message'] ?? 'An error occurred', $error['status'] ?? 500);
    }

    protected function getErrorMessage(string $type, ?string $subType = null): array
    {
        if ($subType && isset($this->messages[$type][$subType])) {
            return $this->messages[$type][$subType];
        }

        if (isset($this->messages[$type])) {
            return $this->messages[$type];
        }

        return [
            'message' => 'An error occurred',
            'status'  => 500,
        ];
    }

    protected function isApiRequest(Request $request): bool
    {
        return $request->wantsJson() || $request->ajax();
    }
}
