<?php

namespace Thorne\SecurityLayer\Exceptions;

use Exception;
use Illuminate\Http\Request;

class SecurityLayerConfigurationException extends Exception
{
    protected array $messages = [
        'signature' => [
            'missing_secret' => [
                'message' => 'Security Layer signature secret is missing',
                'status'  => 500,
            ],
        ],
        'config' => [
            'disabled' => [
                'message' => 'Security Layer is disabled',
                'status'  => 500,
            ],
            'alias_not_found' => [
                'message' => 'Security Layer alias not found',
                'status'  => 500,
            ],
            'class_not_found' => [
                'message' => 'Security Layer class not found',
                'status'  => 500,
            ],
        ],
    ];

    public function __construct(string $type, string $key)
    {
        $message = $this->messages[$type][$key]['message'] ?? 'Unknown configuration error';
        $status  = $this->messages[$type][$key]['status']  ?? 500;

        parent::__construct($message, $status);
    }

    public function render($request)
    {
        if ($this->isApiRequest($request)) {
            return response()->json([
                'message' => $this->getMessage(),
                'status'  => $this->getCode(),
            ], $this->getCode());
        }

        return response($this->getMessage(), $this->getCode());
    }

    protected function isApiRequest(Request $request): bool
    {
        return $request->wantsJson() || $request->ajax();
    }
}
