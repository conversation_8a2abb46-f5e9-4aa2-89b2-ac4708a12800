<?php

namespace <PERSON>\SecurityLayer\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use <PERSON>\SecurityLayer\Exceptions\SecurityLayerExceptionHandler;
use <PERSON>\SecurityLayer\Services\SessionService;
use <PERSON>\SecurityLayer\Traits\LogsMiddleware;
use <PERSON>\SecurityLayer\Traits\ResolvesModule;
use Thorne\SecurityLayer\Traits\SecureContext;

class SessionMiddleware
{
    use LogsMiddleware, ResolvesModule, SecureContext;

    protected SessionService $sessionService;

    protected SecurityLayerExceptionHandler $handler;

    public function __construct(SessionService $sessionService, SecurityLayerExceptionHandler $handler)
    {
        $this->sessionService = $sessionService;
        $this->handler        = $handler;
    }

    public function handle(Request $request, Closure $next)
    {
        $moduleKey     = $this->resolveModuleKey($request, 'session');
        $sessionConfig = $this->resolveModuleConfig($moduleKey, 'session');

        if (! ($sessionConfig['enabled'] ?? false)) {
            return $next($request);
        }

        $token             = $request->header('X-Customer-Token');
        $requestCustomerId = $request->header('X-Customer-Id');

        $this->logInfo('session', 'Checked: "session" middleware', [
            'moduleKey'  => $moduleKey,
            'token'      => $token ? 'present' : 'missing',
            'customerId' => $requestCustomerId ?: 'missing',
        ]);

        if (! $requestCustomerId) {
            $this->logWarning('session', 'Missing: "session" middleware', [
                'moduleKey' => $moduleKey,
                'header'    => 'X-Customer-Id',
            ]);

            return $this->handler->handle($request, 'session', 'missing', [
                'header' => 'X-Customer-Id',
            ]);
        }

        if (! $token) {
            $this->logWarning('session', 'Missing: "session" middleware', [
                'moduleKey' => $moduleKey,
                'header'    => 'X-Customer-Token',
            ]);

            return $this->handler->handle($request, 'session', 'missing_token', [
                'header' => 'X-Customer-Token',
            ]);
        }

        if (! preg_match('/^[a-zA-Z0-9-_]+\.[a-zA-Z0-9-_]+\.[a-zA-Z0-9-_]+$/', $token)) {
            $this->logWarning('session', 'Invalid Format: "session" middleware', [
                'moduleKey' => $moduleKey,
                'token'     => $token,
            ]);

            return $this->handler->handle($request, 'session', 'invalid_format');
        }

        $tokenCustomerId = $this->sessionService->validateSession($token, $request);

        if (! $tokenCustomerId) {
            $this->logWarning('session', 'Invalid: "session" middleware', [
                'moduleKey' => $moduleKey,
                'token'     => $token,
            ]);

            return $this->handler->handle($request, 'session', 'invalid');
        }

        if ($tokenCustomerId !== $requestCustomerId) {
            $this->logWarning('session', 'Mismatch: "session" middleware', [
                'moduleKey'         => $moduleKey,
                'tokenCustomerId'   => $tokenCustomerId,
                'requestCustomerId' => $requestCustomerId,
            ]);

            return $this->handler->handle($request, 'session', 'mismatch');
        }

        $this->logInfo('session', 'Valid: "session" middleware', [
            'moduleKey'  => $moduleKey,
            'customerId' => $tokenCustomerId,
        ]);

        $this->setSecureContextMany($request, [
            'customer_id'                        => $tokenCustomerId,
            'session_token'                      => $token,
            'is_authenticated_by_security_layer' => true,
        ]);

        return $next($request);
    }
}
