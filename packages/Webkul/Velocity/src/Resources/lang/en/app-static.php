<?php

return [
    'congratulations' => 'Congratulations',
    'homepage'        => [
        'download'             => 'Download',
        'drsp'                 => 'Digital Real Estate Share Certificate (DRESC)',
        'search-text'          => 'Search...',
        'content'              => 'Real estate investors from all regions of our country will love our investment platform, which is fast, secure, user-friendly, and available 24/7.',
        'content-line'         => 'With our expert team, we are always by your side!',
        'easy-buy-sell'        => 'Easy Buying and Selling',
        'buy-sell'             => 'Buy/Sell',
        'drsp-card'            => 'You can instantly buy or sell your real estate properties using "digital real estate share certificates" as you please.',
        'mfnft-title'          => 'Multi-Fractional Non-Fungible Token/ MFNFT',
        'mfnft-card'           => 'Token holders have ownership rights to rental income and other proceeds generated from the real estate properties. They can also participate in community decisions regarding the real estate by exercising their voting rights.',
        'nft-title'            => 'NFT (Non-Fungible Token) Documentation',
        'nft-card'             => 'You can mint your real estate properties as NFTs (Non-Fungible Tokens) and convert them into cash as desired on national and international marketplaces.',
        'ensure'               => 'How Do We Achieve This?',
        'ensure-comment'       => '<PERSON> prepares you for faster, more secure, and profitable real estate investments by utilizing blockchain technology.',
        'ensure-text'          => 'In this new technological world where you come across terms like blockchain, NFTs, Web 3, DeFi, we aim to guide you through.',
        'text'                 => 'You can buy, sell, and generate rental income from real estate properties.',
        'start-button'         => 'Get Started',
        'portfolio'            => 'Become one of those who buy and sell the digital real estate share certificates (DRESC) of the products in our portfolio!',
        'terramirum-text'      => 'Our marketplace (TerraMirum) is presented as a modern investment space based on blockchain technology to virtual world investors, with an approximate transaction volume of 3 trillion USD, aiming to prevent them from disconnecting from the real world.',
        'tenant'               => 'Tenant',
        'thorne'               => 'Thorne',
        'income'               => 'Income',
        'convert'              => 'Converts to Token',
        'token'                => 'Token Holders Receive It',
        'send-bank'            => 'Sends to Your Bank Account',
        'slider-text'          => 'Blockchain Based Real Estate',
        'slider-text-line'     => 'Investment Platform',
        'subscribe-newsletter' => 'Subscribe to our newsletter',
        'sign-up'              => 'Sign Up',
        'kvvk-agree-text'      => 'I accept the Privacy Statement and KVKK Policy',
        'subscribe-ok'         => 'Email added to newsletter list',
        'check-email'          => 'Check your email address!',
        'check-kvkk'           => 'Make sure you approve the contracts!',
        'contact'              => [
            'submit-success' => 'Your message has been sent successfully.',
            'submit-error'   => 'Your message could not be sent.',
        ],
    ],

    'subscriber' => [
        'subscribe'       => 'Subscribe to our newsletter',
        'kvkk'            => 'I accept the Privacy Statement and KVKK Policy',
        'subscriber-text' => 'Thorne Bilişim A.Ş. aims to develop digital solution proposals in the real estate sector, starting with the selection of the "Model Digital Product."',
    ],

    'products' => [
        'product-cat-title'   => 'In the category the most luxurious ads',
        'product-cat-text'    => '3rd most rated post',
        'breadcrumb-part1'    => 'Home',
        'breadcrumb-part2'    => 'Housing',
        'breadcrumb-land'     => 'Land',
        'unit-price'          => 'm2 Price',
        'unit-price-land'     => 'Unit Price',
        'value'               => 'Value',
        'drsp-sales'          => 'DRESC Sales Rate',
        'drsp-unit-price'     => 'DRESC Share Unit Price',
        'property-value'      => 'Value of the Real Estate',
        'drsp-total'          => 'DRESC Total Share Amount',
        'using'               => 'Residential Use',
        'type'                => 'Building/Land Type',
        'usage-area'          => 'Usega Area',
        'using-status'        => 'Using Status',
        'details'             => 'Details',
        'bath-color'          => 'Bathroom Color',
        'kitchen-color'       => 'Kitchen Color',
        'room'                => 'Room',
        'usage-area-sqfit'    => 'Residential Use / Sq Fit',
        'parking'             => 'Car Park',
        'price'               => 'Price',
        'land-city'           => 'City',
        'land-district'       => 'District',
        'land-neighborhood'   => 'Neighborhood',
        'land-ada'            => 'Block',
        'land-parcel'         => 'Plot',
        'land-expertise'      => 'Expertise',
        'land-area'           => 'Area',
        'land-kind'           => 'Kind',
        'land-status'         => 'Current Situation',
        'land-unit-price'     => 'Unit Price',
        'land-total-price'    => 'Total Price',
        'land-total-nft'      => 'Total NFT',
        'land-nft-unit-price' => 'NFT Unit Price',
        'land-sold-nft'       => 'Sold NFT',
        'land-sale-nft'       => 'NFT On Sale',
        'land-increase'       => 'NFT Value Increase',
    ],

    'cart' => [
        'page-title'      => 'Cart',
        'cart-text'       => 'My Cart',
        'empty-msg'       => 'Your Cart is Empty',
        'empty-btn-msg'   => 'Continue Reviewing',
        'share-amount'    => 'Quantity',
        'total-amount'    => 'Total (Share) Amount',
        'remainig-share'  => 'Unit Amount',
        'address'         => 'My invoice will come to the same address.',
        'continue'        => 'Continue',
        'payment-methods' => 'Payment methods',
        'payment-options' => 'Payment options',
        'address-info'    => 'Address information',
        'share-info'      => 'Share Information',
        'campaign-info'   => 'discount applied within the',
        'discount'        => 'Discount',
        'discount-amount' => 'Discounted Amount',
    ],

    'mini-cart' => [
        'total' => 'Total',
        'cart'  => 'Cart',
        'share' => 'Number of Shares',
        'pay'   => 'Pay',
    ],

    'login' => [
        'remember'             => 'Remember Me',
        'continue'             => 'Continue without a member',
        'signup'               => 'Sign Up!',
        'signin'               => 'Sign in to your TerraMirum account',
        'page-title'           => 'Sign In Page',
        'singin-button'        => 'Sign In',
        'forgot-your-password' => 'Forgot your password?',
        'email-required'       => 'Email required.',
        'password-placeholder' => 'Password',
        'email-placeholder'    => 'E-mail',
        'email-error-text'     => 'Please Make sure you entered the correct Email.',
        'password-error-text'  => 'Please Make sure you entered the correct password.',
    ],

    'signup' => [
        'hello'           => 'Hello',
        'start-msg'       => 'Create an account, do not miss the opportunity!',
        'terms'           => 'I accept the terms of membership by clicking on "Sign Up".',
        'id'              => 'ID Number',
        'verify'          => 'Verify',
        'phone'           => 'Phone',
        'pass-confirm'    => 'Password Confirm',
        'uname'           => 'Username',
        'btn-text'        => 'Sign Up',
        'nationality'     => 'Nationality',
        'for-personal'    => 'Personal Register',
        'for-instutional' => 'Instutional Register',
        'personal-info'   => 'Authorized User Information',
        'company-info'    => 'Company Information',
        'place-of-birth'  => 'Place of Birth',
        'personal-wallet' => 'Wallet Address (Optional)',
    ],
    'forgot-password' => [
        'page-title'        => 'Forgot Password',
        'section-title'     => 'Having trouble logging in?',
        'email'             => 'Email',
        'remember-password' => 'Did you remember your password?',
        'submit-btn'        => 'Send Reset Mail',
    ],
    'reset-password' => [
        'page-title'        => 'Reset Password',
        'section-title'     => 'Having trouble logging in?',
        'email'             => 'Email',
        'new-password'      => 'New Password',
        'confirm-password'  => 'Confirm New Password',
        'remember-password' => 'Did you remember your password?',
        'submit-btn'        => 'Reset My Password',
    ],
    'updated-password' => [
        'page-title'    => 'Success',
        'section-title' => 'Your password has been successfully reset.',
        'login-btn'     => 'Login',
    ],
    'verification' => [
        'page-title' => 'Verification',
        'hello'      => 'Hello',
        'start-msg'  => 'Please verify your account to continue.',

        'email' => [
            'required' => 'Email is required',
            'email'    => 'Email is not valid',
            'unique'   => 'Email is already taken',
        ],
        'id_number' => [
            'required' => 'ID Number is required',
            'numeric'  => 'ID Number must be numeric',
            'digits'   => 'ID Number must be 11 digits',
            'unique'   => 'ID Number is already taken',
        ],
        'first_name' => [
            'required' => 'First Name is required',
            'string'   => 'First Name must be string',
        ],
        'last_name' => [
            'required' => 'Last Name is required',
            'string'   => 'Last Name must be string',
        ],
        'date_of_birth' => [
            'required'    => 'Date of Birth is required',
            'date_format' => 'Date of Birth must be in format of YYYY-MM-DD',
        ],
        'phone' => [
            'required' => 'Phone is required',
        ],
        'user_name' => [
            'required' => 'Username is required',
        ],
    ],
    'verification-pending' => [
        'page-title'  => 'Verification',
        'hello'       => 'Hello',
        'start-msg'   => 'Verification is in progress. Please try again later.',
        'contact-msg' => 'Your verification is in progress. Need help? Contact us.',
        'verify-info' => 'Your account verification process has not been completed, but you can make purchases. Confirmation must be completed for withdrawals.',
    ],
    'category' => [
        'price-range'    => 'Price Range',
        'all-categories' => 'All Categories',
    ],
    'wishlist' => [
        'title'         => 'Wishlist',
        'empty-msg'     => 'There are no products in your wishlist.',
        'empty-content' => 'Discover the tokens of special real estate, divided into thousands of shares.',
        'btn-text'      => 'Discover',
    ],
    'address' => [
        'empty-msg'     => 'There are no address in your profile.',
        'empty-content' => 'You do not have any saved addresses here, please try to create it by clicking the add button.',
    ],

    'user-profile' => [
        'wallet'             => 'Wallet/Orders',
        'wallet-address'     => 'Wallet Address',
        'empty-wallet'       => 'Your wallet is empty',
        'table-product-name' => 'Product',
        'table-actions'      => 'Actions',
        'contract-button'    => 'Smart Contract',
        'contract-notfound'  => 'Smart Contract Not Found',
        'advantage'          => 'Advantages',
        'wishlist'           => 'Wishlist',
        'notification'       => 'Notifications',
        'address'            => 'My Addresses',
        'logout'             => 'Logout',
        'preferences'        => 'Communication Preferences',
        'user-info'          => 'User Information',
        'account-info'       => 'Account Information',
        'profile'            => 'Information Profile',
        'info'               => 'You can change your username a maximum of 2 times.',
        'change'             => 'Change',
        'pass-change'        => 'Password Change',
        'pass-change-msg'    => 'Your password must contain at least one letter, number or special character. Also, your password must be at least 8 characters.',
        'info-msg'           => 'Within the scope of the Consent Text, you can specify the methods you prefer to be informed about important campaigns.',
        'info-email'         => 'I would like to receive e-mail about campaigns and market bulletins that may interest me.',
        'info-sms'           => 'I would like to receive SMS about campaigns and market bulletins that may interest me.',
        'info-tel'           => 'I would like to receive calls about campaigns and market bulletins that may interest me.',
        'info-text'          => 'When you turn off your communication preferences regarding the campaigns, you can continue to receive e-mail, notification, SMS or phone call regarding your membership settings.',
        'info-part'          => 'You can specify the methods you prefer to be informed about important campaigns within the scope of',
        'consent-text'       => 'Consent Text',
        'amount-vue'         => 'Amount',
        'email'              => 'E-mail',
        'sms'                => 'SMS',
        'call'               => 'Phone Call',
        'current-pass'       => 'Current Password',
        'new-pass'           => 'New Password',
        'confirm-pass'       => 'Confirm Password',
        'wallet-tab'         => 'Wallet',
        'orders-tab'         => 'Orders',
        'nft-tab'            => 'My NFTs',
        'trans-tab'          => 'Transactions',
        'orders-menu-title'  => 'Wallet/Orders',
        'wallet-currency'    => 'Currency',
        'wallet-amount'      => 'Amount',
        'nft-empty-msg'      => 'Discover the tokens of special real estate and land, divided into thousands of shares.',
        'nft-empty-title'    => 'You don\'t have any NFTs yet.',
        'transactions'       => 'transactions',
        'deposit'            => 'deposit',
        'withdraw'           => 'withdraw',
        'balance'            => 'balance',
        'locked-balance'     => 'locked balance',
    ],

    'orders' => [
        'title'              => 'Your order has been received, Congratulations.',
        'btn-text'           => 'Go to my purchases',
        'content'            => 'Details about your order have been sent to',
        'order-no'           => 'Order Number',
        'billing'            => 'Billing Address',
        'amount'             => 'Amount Paid',
        'bank-info'          => 'Bank Account Information',
        'bank-name'          => 'Bank',
        'account-name'       => 'Account Name',
        'iban'               => 'IBAN',
        'branch-code'        => 'Branch Code',
        'success'            => 'Your order has been successfully created.',
        'success-for-crypto' => 'Your order has been successfully created.',
        'thanks'             => '<p class="p-5 pt-2 fs-5 lh-base text-center"><span class="text-warning">Thank you for joining the MIRUM Token family!</span> <br>Your order has been created successfully. <br>You can access order details on the <a href="/customer/account/orders">My Orders</a> page and see the status of your order.<br>Once you make the payment for the order amount to the account information below, along with the<span style="color:#ff9700;">order number</span>, and your<span style="color:#ff9700;">KYC</span> process is completed, your payment will be secured in your wallet.<br>Once the transfer is complete, we will contact you via <span style="color:#ff9700;">email</span>. <br>For more information, you can go to the <a href="/pages/faq">FAQ page</a>or write to us on the <a href="/contact-us">Contact page</a>.</p>',
        'thanks-for-crypto'  => '<p class="p-5 pt-2 fs-5 lh-base text-center"><span class="text-warning">Thank you for joining the MIRUM Token family!</span> <br>Your order has been created successfully. <br>You can access your order details from the <a href="/customer/account/orders">My Orders</a> page and check its status.<br>Once your <span style="color:#ff9700;">KYC</span> process is complete, the transfer will be made to your wallet. <br>Once the transfer is complete, we will contact you via <span style="color:#ff9700;">email</span>.For more information, you can check the <a href="/pages/faq">FAQ page</a> or <a href="/contact-us">Contact Us</a>.</p>',
    ],

    'advantages' => [
        'title'           => 'Advantages',
        'coupons'         => 'My Coupons',
        'use'             => 'Use It',
        'purchase-amount' => 'Purchase Amount',
        'conditions'      => 'Conditions',
        'expire-date'     => 'Expiration Date',
        'last'            => 'The last',
        'days'            => 'days',
        'campaigns'       => 'Campaigns',
        'discount'        => 'Discount',
        'opportunity'     => 'Opportunity',
        'favorite'        => 'Add Favorites',
        'notify'          => 'Be the First to Notify!',
        'add-fav'         => 'Add to your favourites!',
        'expired'         => 'Expired',
        'info-msg'        => 'Start adding favorites now, be the first to know about the deals on the big discount days on',
        'empty-msg'       => 'There is no defined advantage yet.',
    ],
    'profile' => [
        'index' => 'Account',
    ],
    // # ilave ##
    'profilePage' => [
        'profile'                                      => 'Profile',
        'user-name'                                    => 'Username',
        'last-name'                                    => 'Last Name',
        'panel-button'                                 => 'Change',
        'company-button'                               => 'Apply',
        'my-profile'                                   => 'My Profile',
        'profile-orders'                               => 'Orders',
        'profile-wallet'                               => 'Wallet',
        'logout'                                       => 'Logout',
        'first-name'                                   => 'First Name',
        'profile-account'                              => 'Account',
        'user-phone'                                   => 'Phone Number',
        'user-email'                                   => 'E-mail Adresi',
        'profile-security'                             => 'Security',
        'profile-page'                                 => 'Profile Page',
        'new-password'                                 => 'New Password',
        'profile-locked-coins'                         => 'Locked Tokens',
        'current-password'                             => 'Current Password',
        'confirm-password'                             => 'Confirm Password',
        'profile-transfer-details'                     => 'Transfer Details',
        'two-factor-authentication'                    => 'Two Factor Authentication',
        'google-two-factor-authentication-is-verified' => 'Google Two Factor Authentication is Verified',
        'institutional'                                => 'For institutional',
        'company-name'                                 => 'Company Name',
        'company-mail'                                 => 'Company Mail',
        'company-tax-number'                           => 'Company Register number',
        'company-phone'                                => 'Company Phone',
        'company-representative'                       => 'Company Representative',
        'company-address'                              => 'Company Address',
        'legal-form'                                   => 'Legal Form',
        'company-name-error'                           => 'Company Name field is required.',
        'company-mail-error'                           => 'Company Email field is required.',
        'company-tax-number-error'                     => 'Company Registration Number field is required.',
        'company-phone-error'                          => 'Company Phone field is required.',
        'company-representative-error'                 => 'Company Representative field is required.',
        'company-address-error'                        => 'Company Address field is required.',
        'legal-form-error'                             => 'Legal Form field is required.',
        'register-form'                                => 'Register Form',
        'error-message'                                => 'Please fill in the fields below',
        'personal-address'                             => 'Address',
        'legal-type'                                   => 'Legal Type',
    ],
    // # ilave ##
    'walletPage' => [
        'wallet'                                                        => 'Wallet',
        'panel-balance'                                                 => 'Balance',
        'panel-currency'                                                => 'Chain Currency',
        'wallet-page'                                                   => 'Wallet Page',
        'select-label-text'                                             => 'Select Chain',
        'address-label-text'                                            => 'Wallet Address',
        'panel-pending-balance'                                         => 'Pending Balance',
        'panel-drop-button-deposit-text'                                => 'Deposit',
        'panel-drop-button-withdraw-text'                               => 'WithDraw',
        'panel-drop-menu-button-withcash-text'                          => 'With Cash',
        'panel-modal-content-title-text'                                => 'Deposit With Cash',
        'panel-drop-menu-button-withcrypto-text'                        => 'With Crypto',
        'panel-drop-menu-button-withdraw-withcash-text'                 => 'With Cash',
        'panel-drop-menu-button-deposit-withcash-text'                  => 'With Cash',
        'panel-drop-menu-button-withdraw-withcrypto-text'               => 'With Crypto',
        'panel-drop-menu-button-deposit-withcrypto-text'                => 'With Crypto',
        'panel-modal-content-bank-account-information-iban-text'        => 'IBAN(TRY)',
        'panel-modal-content-bank-account-information-bankname-text'    => 'Bank Name',
        'panel-modal-content-bank-account-information-text'             => 'Bank Account Information',
        'panel-modal-content-bank-account-information-accountname-text' => 'Account Name',
        'panel-modal-content-body2-text'                                => '<p>You must write <span><b>{accountId}</b></span> in the description section</p>',
        'panel-modal-content-body1-text'                                => '<p>To top up the balance, you must make a payment via <span>Money Transfer/EFT</span> to one of the relevant bank addresses.</p>',
        'panel-modal-crypto-content-title-text'                         => 'Deposit With Crypto',
        'panel-modal-crypto-content-h4-title-text'                      => 'You must pay to the following Wallet',
        'panel-modal-crypto-content-body-text'                          => '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce elit ligula, vulputate vel egestas id, scelerisque vitae risus. Vestibulum ut auctor arcu, ac ornare urna. Ut non felis tortor.</p>',
        'panel-modal-content-withdraw-title-text'                       => 'Withdraw Cash',
        'panel-modal-content-withdraw-body-text'                        => '<p>Please Write <span>Google Two Factor Authentication</span> Key on input</p>',
        'panel-modal-content-withdraw-error-title-text'                 => 'Please correct the following error(s):',
        'panel-modal-content-withdraw-button-text'                      => 'Start Withdraw Action',
        'panel-modal-content-withdraw-body2-text'                       => '<p>You can perform transactions by filling in the required fields for the transfer.</p><p><span>You can only transfer to your own account</span></p>',
        'panel-modal-content-withdraw-input-amount-text'                => 'Amount',
        'panel-modal-content-withdraw-textarea-text'                    => 'Write Your Note.',
        'panel-modal-content-withdrawcrypto-title-text'                 => 'Withdraw Crypto',
        'panel-modal-toast-title'                                       => 'Success',
        'panel-model-toast-body'                                        => 'Wallet Address copied successful',
        'panel-modal-content-withdrawsuccess-title-text'                => 'Transfer işlemi başarılı.',
        'panel-modal-google2fa-button-text'                             => 'Change',
        'panel-modal-validate-text'                                     => 'Lütfen zorunlu alanları doldurun.',
    ],
    // # ilave ##
    'lockedCoins' => [
        'locked-coins'            => 'Locked Tokens',
        'panel-amount-text'       => 'Amount',
        'panel-currency-text'     => 'Currency',
        'panel-title-text'        => 'Release Type',
        'select-coin-label-text'  => 'Select Coin',
        'locked-coins-page'       => 'Locked Tokens Page',
        'panel-release-date-text' => 'Release Date',
        'panel-empty-title'       => 'No Locked Tokens Found',
        'panel-empty-text'        => 'There are currently no locked tokens available.',
    ],
    // # ilave ##
    'ordersPage' => [
        'orders'                            => 'Orders',
        'orders-page'                       => 'Orders Page',
        'table-order-id'                    => 'Order ID',
        'table-order-date'                  => 'Order Date',
        'table-order-total'                 => 'Total',
        'table-order-pcs'                   => 'Pcs',
        'table-order-status'                => 'Status',
        'table-order-show'                  => 'Show',
        'table-order-action'                => 'Action',
        'table-order-action-button-text'    => 'Cancel',
        'modal-content-title-text'          => 'Products Ordered',
        'modal-content-table-sku'           => 'COIN',
        'modal-content-table-name'          => 'Name',
        'modal-content-table-pcs'           => 'PCS',
        'modal-content-table-price'         => 'Price',
        'modal-content-table-total'         => 'Total',
        'modal-content-payment-method'      => 'Payment Method',
        'modal-content-payment-subtotal'    => 'Subtotal',
        'modal-content-payment-tax'         => 'Tax',
        'modal-content-payment-grand-total' => 'Grand Total',
        'modal-content-tx-id'               => 'TX ID',
        'modal-content-cancel-text'         => 'Are You Sure You Want to Cancel the Order?',
        'modal-content-cancel-info-text'    => 'Payments made via blockchain cannot be CANCELLED as they are permanently recorded in blocks.',
        'modal-button-cancel-text'          => 'Cancel',
        'modal-button-success-text'         => 'Confirm',
    ],
    // # ilave ##
    'transferDetail' => [
        'transfer-detail-title-text'      => 'Transfer Details',
        'transfer-detail-page-title-text' => 'Transfer Details Page',
        'button-today-text'               => 'Today',
        'button-thisweek-text'            => 'This Week',
        'button-thismonth-text'           => 'This Month',
        'button-lastthreemonth-text'      => 'Last Three Month',
        'table-title'                     => 'Title',
        'table-currency'                  => 'Currency',
        'table-balance'                   => 'Amount',
        'table-blocked-balance'           => 'Blocked Balance',
        'table-date'                      => 'Date',
    ],

    'alert' => [
        'success' => 'Success',
        'error'   => 'Error',
        'warning' => 'Warning',
        'info'    => 'Info',
    ],
    'checkout' => [
        'onepage' => [
            'page-title'                     => 'Checkout',
            'heading'                        => 'Checkout',
            'select-payment-type'            => 'Select Payment Type',
            'select-address'                 => 'Select Address',
            'payment-method-money-transfer'  => 'Money Transfer',
            'payment-method-pay-with-crypto' => 'Pay with Crypto',
            'continue'                       => 'Continue',
            'cart'                           => 'My Cart',
            'total'                          => 'Total Amount',
            'update-cart'                    => 'Update Cart',
        ],
    ],
    'payment' => [
        'summary' => [
            'check-order-question' => 'Your invoice will be prepared and sent to your email address according to the order you have placed.<br><br>Do you confirm your order ?',
            'check-order'          => 'Confirm Order',
            'cancel'               => 'Cancel',
        ],
    ],
    'fast-order' => [
        'summary' => [
            'page-title'                 => 'Buy Gold / Sell Gold',
            'title'                      => 'Buy / Sell Gold',
            'subtitle'                   => 'Select a transaction type and enter the amount of gold to trade at current market rates.',
            'buy-gold'                   => 'Buy Gold',
            'sell-gold'                  => 'Sell Gold',
            'gold-purchase-confirmation' => 'Gold Purchase Confirmation',
            'gold-sale-confirmation'     => 'Gold Sale Confirmation',
            'locked-price'               => 'LOCKED PRICE',
            'transaction-type'           => 'Transaction Type:',
            'gold-amount'                => 'Gold Amount:',
            'unit-price'                 => 'Unit Price:',
            'total-amount'               => 'Total Amount:',
            'remaining-time'             => 'Remaining Time:',
            'seconds'                    => 'seconds',
            'cancel'                     => 'Cancel',
            'confirm'                    => 'Confirm',
            'price-locked-info'          => 'The current price for your transaction has been locked for a limited time. When the time expires, you will be automatically redirected to the trading screen displaying updated market prices.',
            'gold-amount-label'          => 'Gold Amount',
            'money-amount-label'         => 'Money Amount',
            'enter-gold-amount'          => 'Enter gold amount',
            'enter-money-amount'         => 'Enter money amount',
            'add-balance'                => 'Add Balance',
            'live-rate'                  => 'LIVE RATE',
            'time-remaining'             => 'Time remaining:',
            'rate-update-info'           => 'The exchange rate will be automatically updated at the end of the time.',
            'buy-limit-info'             => 'The amount of gold you can buy cannot exceed <b>:amount :currency</b>. For more, <a href=":route" class="text-[#D0AA49] font-bold">add balance</a>.',
            'sell-limit-info'            => 'The amount of gold you can sell cannot exceed <b>:amount :currency</b>.',
        ],
        'result' => [
            'buy' => [
                'page-title'              => 'Order Status',
                'order-success-title'     => 'Gold purchase successful!',
                'order-success-subtitle'  => 'Your order has been created, please follow the steps below.',
                'order-created'           => 'Order Created',
                'order-failed'            => 'Order Failed',
                'order-failed-reason'     => 'There was an issue with your order. Please contact our support team.',
                'order-failed-support'    => 'Contact Support',
                'completed'               => 'Completed',
                'payment-processing'      => 'Payment Processing',
                'processing'              => 'Processing',
                'balance-will-be-loaded'  => 'Balance Will Be Loaded',
                'waiting'                 => 'Waiting',
                'failed'                  => 'An Error Occurred',
                'order-summary'           => 'Order Summary',
                'order-number'            => 'Order Number:',
                'order-content'           => 'Order Content:',
                'order-amount'            => 'Order Amount:',
                'order-status'            => 'Order Status:',
                'order-status-pending'    => '<span class="text-yellow-500">Pending</span>',
                'order-status-processing' => '<span class="text-blue-500">Processing</span>',
                'order-status-completed'  => '<span class="text-green-500">Completed</span>',
                'order-status-failed'     => '<span class="text-red-500">Failed</span>',
                'order-status-canceled'   => '<span class="text-red-500">Canceled</span>',
                'order-date'              => 'Order Date:',
                'wallet'                  => 'Wallet',
                'wallet-after-completion' => '(After order completion)',
                'balance'                 => 'Balance:',
            ],
            'sell' => [
                'page-title'              => 'Order Status',
                'order-success-title'     => 'Gold sale successful!',
                'order-success-subtitle'  => 'Your order has been created, please follow the steps below.',
                'order-created'           => 'Order Created',
                'order-failed'            => 'Order Failed',
                'order-failed-reason'     => 'There was an issue with your order. Please contact our support team.',
                'order-failed-support'    => 'Contact Support',
                'completed'               => 'Completed',
                'payment-processing'      => 'Payment Processing',
                'processing'              => 'Processing',
                'balance-will-be-loaded'  => 'Balance Will Be Loaded',
                'waiting'                 => 'Waiting',
                'failed'                  => 'An Error Occurred',
                'order-summary'           => 'Order Summary',
                'order-number'            => 'Order Number:',
                'order-content'           => 'Order Content:',
                'order-amount'            => 'Order Amount:',
                'order-status'            => 'Order Status:',
                'order-status-pending'    => '<span class="text-yellow-500">Pending</span>',
                'order-status-processing' => '<span class="text-blue-500">Processing</span>',
                'order-status-completed'  => '<span class="text-green-500">Completed</span>',
                'order-status-failed'     => '<span class="text-red-500">Failed</span>',
                'order-status-canceled'   => '<span class="text-red-500">Canceled</span>',
                'order-date'              => 'Order Date:',
                'wallet'                  => 'Wallet',
                'wallet-after-completion' => '(After order completion)',
                'balance'                 => 'Balance:',
            ],
        ],
    ],
    'sell-gold' => [
        'summary' => [
            'page-title' => 'Sell Gold',
            'heading'    => 'Sell Gold',

            'select-payment-type'      => 'Select Payment Type',
            'select-address'           => 'Select Address',
            'select-chain'             => 'Select Chain',
            'select-chain-placeholder' => 'Please select a chain',
            'select-coin'              => 'Select Coin',
            'select-coin-placeholder'  => 'Please select a coin',
            'continue'                 => 'Continue',
            'add-balance'              => 'Add Balance',
            'my-cart'                  => 'My Cart (Exchange)',
            'total'                    => 'Total Amount',
            'maximum-selling'          => 'Maximum selling: <b>:max</b> pcs',
        ],
        'authorization' => [
            'page-title' => 'Sell Gold',
            'heading'    => 'Sell Gold',

            'show-payment-details'  => 'Show Payment Details',
            'chain'                 => 'Blockchain',
            'wallet-balance'        => 'Wallet Balance',
            'processing-amount'     => 'Processing Amount',
            'wallet-gas-fee'        => 'Wallet Gas Fee',
            'transaction-amount'    => 'Transaction Amount',
            'expected-fee'          => 'Expected Fee',
            'expected-mirum-fee'    => 'MIRUM Network Fee',
            'risk-clauses'          => 'I have read and accept the <a class="text-terra-orange" id="privacy-policy">Privacy Policy</a>',
            'terms-conditions'      => 'I have read and accept the <a class="text-terra-orange" id="terms-and-conditions">Terms and Conditions. </a>',
            'data-protect'          => 'I have read and accept the RWA I purchased being stored on Mirum Network.',
            'cancellation-policy'   => 'I have read and accept the <a class="text-terra-orange" id="cancellation-policy">Cancellation Policy</a>',
            'pay-now'               => 'Pay Now',
            'order-confirm'         => 'Order Now',
            'cart'                  => 'My Cart',
            'total'                 => 'Total Amount',
            'continue'              => 'Continue',
            'cart_details'          => 'Cart Details',
            'my_cart'               => 'My Cart',
            'pending-balance-title' => 'Transfer expected order fee',
            'pending-balance-fail'  => 'We have pending payment requests that are currently being processed. Please wait a moment for them to complete.',
            'my-cart'               => 'My Cart (Exchange)',
            'exchange-note'         => 'Do you want to update? <a href=":url" class="font-bold text-[#D0AA49]">Go back</a>',
        ],
        'result' => [
            'completed' => [
                'page-title'  => 'Payment Completed',
                'heading'     => 'Payment Completed',
                'title'       => 'Payment Completed',
                'description' => 'Your payment has been successfully completed. Thank you for your transaction. <br> We will send you an email with the details of your order.',
                'my-orders'   => 'My orders',
            ],
            'failed' => [
                'page-title'  => 'Payment Failed',
                'heading'     => 'Payment Failed',
                'title'       => 'Payment Failed',
                'description' => 'Your payment was unsuccessful. Please check your payment information and try again. <br> If the problem persists, please contact us.',
                'my-orders'   => 'My orders',
            ],
            'pending' => [
                'page-title'  => 'Payment Pending',
                'heading'     => 'Payment Pending',
                'title'       => 'Payment Pending',
                'description' => 'Your payment could not be verified yet, please wait while this process continues in the background. <br> It is being processed and will be completed shortly. We will send you an email with the details of your order.',
                'my-orders'   => 'My orders',
            ],
            'processing' => [
                'page-title'  => 'Payment Processing',
                'heading'     => 'Payment Processing',
                'title'       => 'Payment Processing',
                'description' => 'Your payment has been confirmed, and the purchased coins will be transferred to your wallet as soon as possible. <br> We will send you an email with the details of your order.',
                'my-orders'   => 'My orders',
            ],
        ],
    ],
    'pay-with-iyzico' => [
        'authorization' => [
            'page-title' => 'Credit Card Payment',
            'heading'    => 'Secure Payment with Credit Card',
        ],
        'result' => [
            'completed' => [
                'page-title'  => 'Payment Successful',
                'heading'     => 'Payment Completed',
                'title'       => 'Payment Successful',
                'description' => 'Your payment has been successfully processed. A confirmation email with your invoice will be sent shortly.<br><br>You can track your order in the My Orders section.',
                'my-orders'   => 'View My Orders',
            ],
            'failed' => [
                'page-title'  => 'Payment Failed',
                'heading'     => 'Payment Could Not Be Completed',
                'title'       => 'Transaction Failed',
                'description' => 'We were unable to process your payment. Please check your card details and try again, or use a different payment method.<br><br>If the issue persists, contact customer support.',
                'my-orders'   => 'View My Orders',
            ],
        ],
    ],
    'pay-with-crypto' => [
        'summary' => [
            'page-title'               => 'Pay with Crypto - Summary',
            'heading'                  => 'Pay with Crypto',
            'select-payment-type'      => 'Select Payment Type',
            'select-address'           => 'Select Address',
            'select-chain'             => 'Select Chain',
            'select-chain-placeholder' => 'Please select a chain',
            'select-coin'              => 'Select Coin',
            'select-coin-placeholder'  => 'Please select a coin',
            'continue'                 => 'Continue',
            'add-balance'              => 'Add Balance',
            'my-cart'                  => 'My Cart',
            'total'                    => 'Total Amount',

            'deposit-with-crypto'             => 'Deposit with Crypto',
            'deposit-with-crypto-warning'     => 'There is not enough balance in your account. For your order, you must transfer crypto money in the amount of your order to the wallet address below.',
            'deposit-with-crypto-description' => 'You must pay to the following Wallet',
        ],
        'authorization' => [
            'page-title'            => 'Pay with Crypto - Authorization',
            'heading'               => 'Pay with Crypto',
            'show-payment-details'  => 'Show Payment Details',
            'chain'                 => 'Blockchain',
            'wallet-balance'        => 'Wallet Balance',
            'processing-amount'     => 'Processing Amount',
            'wallet-gas-fee'        => 'Wallet Gas Fee',
            'transaction-amount'    => 'Transaction Amount',
            'expected-fee'          => 'Expected Fee',
            'expected-mirum-fee'    => 'MIRUM Network Fee',
            'risk-clauses'          => 'I have read and accept the <a class="text-terra-orange" id="privacy-policy">Privacy Policy</a>',
            'terms-conditions'      => 'I have read and accept the <a class="text-terra-orange" id="terms-and-conditions">Terms and Conditions. </a>',
            'data-protection'       => '<a class="text-terra-orange" id="data-protection">I accept that the RWAs I have purchased will be stored on the Mirum Network.</a>',
            'data-protect'          => 'I have read and accept the RWA I purchased being stored on Mirum Network.',
            'cancellation-policy'   => 'I have read and accept the <a class="text-terra-orange" id="cancellation-policy">Cancellation Policy</a>',
            'pay-now'               => 'Pay Now',
            'order-confirm'         => 'Order Now',
            'cart'                  => 'My Cart',
            'total'                 => 'Total Amount',
            'continue'              => 'Continue',
            'cart_details'          => 'Cart Details',
            'my_cart'               => 'My Cart',
            'pending-balance-title' => 'Transfer expected order fee',
            'pending-balance-fail'  => 'We have pending payment requests that are currently being processed. Please wait a moment for them to complete.',
        ],
        'result' => [
            'completed' => [
                'page-title'  => 'Payment Completed',
                'heading'     => 'Payment Completed',
                'title'       => 'Payment Completed',
                'description' => 'Your payment has been successfully completed. Thank you for your transaction. <br> We will send you an email with the details of your order.',
                'my-orders'   => 'My orders',
            ],
            'failed' => [
                'page-title'  => 'Payment Failed',
                'heading'     => 'Payment Failed',
                'title'       => 'Payment Failed',
                'description' => 'Your payment was unsuccessful. Please check your payment information and try again. <br> If the problem persists, please contact us.',
                'my-orders'   => 'My orders',
            ],
            'pending' => [
                'page-title'  => 'Payment Pending',
                'heading'     => 'Payment Pending',
                'title'       => 'Payment Pending',
                'description' => 'Your payment could not be verified yet, please wait while this process continues in the background. <br> It is being processed and will be completed shortly. We will send you an email with the details of your order.',
                'my-orders'   => 'My orders',
            ],
            'processing' => [
                'page-title'  => 'Payment Processing',
                'heading'     => 'Payment Processing',
                'title'       => 'Payment Processing',
                'description' => 'Your payment has been confirmed, and the purchased coins will be transferred to your wallet as soon as possible. <br> We will send you an email with the details of your order.',
                'my-orders'   => 'My orders',
            ],
        ],
    ],
    'moneytransfer' => [
        'authorization' => [
            'page-title'              => 'Money Transfer',
            'heading'                 => 'Money Transfer',
            'fee-note'                => 'Mirum Network does not charge transaction fees for transactions made through its network.',
            'details'                 => 'Details',
            'operation-fee'           => 'Operation Fee',
            'network-fee'             => 'Network Fee',
            'grand-total'             => 'Grand Total',
            'company-confirm'         => 'I confirm that I am applying on behalf of the company',
            'risk-clauses'            => 'I have read and accept the <a class="text-terra-orange" id="privacy-policy">Privacy Policy.</a>',
            'terms-conditions'        => 'I have read and accept the <a class="text-terra-orange" id="terms-and-conditions">Terms and Conditions. </a>',
            'data-protect'            => 'I have read and accept the RWA I purchased being stored on Mirum Network.',
            'email-confirmation'      => 'I agree to have information sent to me via <span class="text-terra-orange">E-Mail</span>',
            'personal-confirmation'   => 'I confirm that I am applying on my own behalf, I have read and accept.',
            'whitepaper-confirmation' => 'I read the Information Memorandum and I am aware of the risks identified in the <a class="text-terra-orange" id="whitepaper">Information Memorandum.</a>',
            'cancellation-policy'     => 'I have read and accept the <a class="text-terra-orange" id="cancellation-policy">Cancellation Policy</a>',
            'total'                   => 'Total Amount',
            'pay-now'                 => 'Pay Now',
            'order-confirm'           => 'Order Now',
            'vat'                     => 'VAT',
            'buy-button'              => 'BUY',
            'continue'                => 'CONTINUE',
            'money-transfer-fail'     => 'You have a pending order by money transfer.<br />You cannot place a second money transfer order until your order is completed.<br />Please cancel the order or contact us for order cancellation.',
            'data-protection'         => '<a href="#" class="text-terra-orange" id="risk-clauses">I accept that the RWAs I purchased will be stored on the Mirum Network.</a>',
        ],
        'success' => [
            'bank-info-not-exist' => [
                'heading'     => 'Bank Information Could Not Be Accessed',
                'description' => 'Your payment has been successfully completed. Thank you for your transaction. <br> We will send you an email with the details of your order.',
                'contact-us'  => 'Please contact us for bank account information.',
            ],
        ],
    ],

    'home' => [
        'main' => [
            'browser_not_supported'  => 'Your browser does not support the video tag.',
            'banner_title'           => 'The New Name of Investment in <br>Real World Assets with <span>TerraMirum</span>:<br><span style="color: #12D176">Mirum Token</span>',
            'ready_to_sale'          => 'First Round',
            'seed_sale'              => 'First Round',
            'private_sale'           => 'Second Round',
            'public_sale'            => 'Third Round',
            'metric_seed_sale'       => 'First Round :date',
            'metric_private_sale'    => 'Second Round :date',
            'metric_public_sale'     => 'Third Round :date',
            'coming_soon'            => 'Coming Soon',
            'private_sale_countdown' => [
                'styleText' => 'second round',
                'noneText'  => 'will start in..',
            ],
            'public_sale_countdown' => [
                'styleText' => 'third round',
                'noneText'  => 'will start in..',
            ],
            // 'private_sale_countdown' => '<span style="color: #FF9700;">second round</span> Will Start In..',
            'about_title'                          => 'Are you ready to step up in the digital era?',
            'about_content'                        => 'Our ICO offers a unique opportunity. Check out our Information Memorandum and don\'t miss the chance to build the future together!',
            'read_whitepaper'                      => 'Read Information Memorandum',
            'why_choose_us'                        => 'Why Choose Us',
            'choose_our_token'                     => 'Why choose our <br>Mirum Token?',
            'simplifying_payments'                 => 'Simplifying the Payment Process',
            'simplifying_payment_details'          => 'Unlock a world of financial ease with our commitment to simplifying the payment process, ensuring seamless transactions and unparalleled convenience in every interaction.',
            'timeless_transactions'                => 'Transactions designed to be timeless.',
            'timeless_transaction_details'         => 'Embrace a financial journey where each transaction is meticulously crafted to withstand the passage of time, ensuring a seamless and enduring legacy for your financial endeavors.',
            'secure_wealth'                        => 'Secure Your Wealth, Retain Financial Command!',
            'secure_wealth_details'                => 'Navigate the financial landscape with confidence – where your wealth is secure, and control is in your hands!',
            'privacy_priority'                     => 'Ensuring user privacy is our top priority.',
            'privacy_priority_details'             => 'Your trust is paramount to us. Rest assured, we are dedicated to creating a secure environment where your data is guarded, and your online experience is worry-free.',
            'mirum_chain_advantages'               => 'Advantages of Mirum Protocol to Blockchain',
            'mirum_chain_advantages_details'       => 'Diversity and Flexibility <br>IBC Interaction<br>NFT Rental Deals<br>Staking and Passive Income with PoS<br>Community Engagement',
            'token_metrics'                        => 'Token Metrics',
            'seed_sale_date'                       => 'First Round 14/10/2024',
            'seed_sale_amount'                     => ':amount Mirum',
            'private_sale_date'                    => 'Second Round 02/11/2024',
            'private_sale_amount'                  => ':amount Mirum',
            'public_sale_date'                     => 'Third Round 30/11/2024',
            'public_sale_amount'                   => ':amount Mirum',
            'seed_sale_tab'                        => '<span class="yellow">First</span> Round',
            'private_sale_tab'                     => '<span class="purple">Second</span> Round',
            'public_sale_tab'                      => '<span class="green">Third</span> Round',
            'seed_sale_price'                      => '1 MIRUM = 0,0040 €',
            'seed_sale_info'                       => 'A total of 12.500.000 MIRUM tokens will be offered for sale through the seed round sale. These tokens will be sold at the most advantageous price of 0,0040 Euros, and these tokens will begin to be unlocked 8 months after the ICO ends. Afterwards, locks will be opened at a rate of 20% every month.',
            'private_sale_price'                   => '1 MIRUM = 0,0072 €',
            'private_sale_info'                    => 'A total of 20.833.333 MIRUM tokens will be available for sale during the second round. These tokens will be sold at the advantageous price of 0,0072 Euros, and these tokens will begin to be unlocked 6 months after the ICO ends. Afterwards, locks will be opened at a rate of 10% every month.',
            'public_sale_price'                    => '1 MIRUM = 0,0120 €',
            'public_sale_info'                     => 'A total of 66.666.667 MIRUM tokens will be available for sale during the third round. The sale of these tokens will be sold at 0,0120 Euros and these tokens will start to be unlocked 3 months after the ICO ends. Afterwards, locks will be opened at a rate of 10% every month.',
            'disclaimer'                           => '<span class="text-danger">*</span> Please read the Information Memorandum before purchasing.',
            'our_roadmap'                          => 'Our Roadmap',
            'terramirum_strategy_and_project_plan' => 'Terramirum Strategy and Project Plan',
            'mid_of_q3_2021'                       => '2021 (Starting Point)',
            //            'starting_point' => 'Starting Point',
            'starting_point_description' => 'This is the period when the housing problem that emerged with the pandemic led to a massive housing demand boom. The resulting supply problems were met with great concern, especially by central governments who demanded that the banking sector create new sources of liquidity to support housing production. Obstacles to the development of the real estate sector were identified.  An ideation phase was launched to find solutions to the various obstacles, especially liquidity.',
            //            'obstacles_identified' => 'Obstacles Identified',
            //            'idea_phase_begun' => 'Idea Phase Begun',
            'mid_of_q1_2022' => 'First half of 2022',
            //            'find_solutions_for_real_estate_sectors' => 'Find Solutions for Real Estate Sectors',
            'find_solutions_for_real_estate_sectors_description' => 'The roadmap was planned to find solutions to the problems of the real estate sector. The idea of bringing together technology-enabled problem solvers in a technology platform to manage the production and consumption of housing (the initial idea phase of product development was completed). Basic design of Web2 models was done. Phase 2 company procedures completed.',
            //            'web2_models_basic_design_completed' => 'Web2 Models Basic Design Completed',
            //            'company_procedures_completed_for_stage_2' => 'Company Procedures Completed for Stage 2',
            'mid_of_q2_2022' => 'Second half of 2022',
            //            'basic_design_of_web3_models_was_done' => 'Basic Design of Web3 Models was Done',
            'web3_models_basic_design_completed' => 'Rising real estate prices and the rapid decline in purchasing power around the world have created a new way to invest in real estate: Real estate shareholding (Second ideation phase of product development completed). The basic design of the Web3 models has been done. It was decided to produce Non-Fungible Tokens and Semi Fungible Tokens for real estate shareholding. Company structure expanded. Website design started.',
            //            'produce_nfts_and_sfts_for_real_estate_share_ownership' => 'Produce NFTs and SFTs for Real Estate Share Ownership',
            //            'company_structure_expanded' => 'Company Structure Expanded',
            //            'website_design_started' => 'Website Design Started',
            'mid_of_q1_2023' => 'First half of 2023',
            //            'cityfund_was_created_to_meet_tokenization_needs' => 'CityFund was Created to Meet Tokenization Needs',
            'support_agreements_aimed_with_key_companies' => 'In order to realize the project, it was aimed to make support agreements with important companies investing in real estate. It was decided to conduct preliminary research on the “Seed Investment” process. Research was conducted on the infrastructure for whitelabeling. It was decided to plan TerraMirum, a common marketplace for all products. “CityFund” infrastructure was established to meet the needs of “tokenization” and the project was explained to various local governments and non-governmental organizations. It was decided to design city piggy banks for investment in cities. With the obstacles that came to the fore, it was decided to proceed with the asset investment process.',
            //            'negative_end_of_seed_investment_process' => 'Negative End of Seed Investment Process',
            //            'plan_for_terramirum_marketplace' => 'Plan for Terramirum Marketplace',
            //            'creation_of_urban_piggy_banks_for_investment' => 'Creation of Urban Piggy Banks for Investment',
            'mid_of_q2_2023' => 'Second half of 2023',
            //            'advertising_activities_have_started' => 'Advertising Activities Have Started',
            'focus_on_marketing_projects' => 'It was decided to focus on marketing projects to increase our market share. Started small scale promotional advertising activities. Research began to be conducted for conducting market activities. Generated alternatives for expanding advertising activities. Needs were identified to continue expanding the market. Promotion in the international arena and global cooperation models were explored. Started to create different information communities for consumers.',
            //            'carrying_out_market_activities' => 'Carrying out Market Activities',
            //            'expansion_of_advertising_activities' => 'Expansion of Advertising Activities',
            //            'continued_market_expansion' => 'Continued Market Expansion',
            //            'promotion_in_international_arena' => 'Promotion in International Arena',
            //            'global_cooperation' => 'Global Cooperation',
            //            'community_building_in_marketplace' => 'Community Building in Marketplace',
            'mid_of_q2_2024_1' => 'First Quarter of 2024',
            //            'launchpad' => 'Launchpad',
            'launchpad_setup_available_for_purchase' => 'Launchpad installation infrastructure has been prepared, which you can purchase at a pre-sale price. Initiation of technology infrastructure applications related to requirements.',
            //            'launching_applications_related_to_requirements' => 'Launching Applications Related to Requirements',
            //            'marketplace_dashboard_tests_continue' => 'Marketplace Dashboard Tests Continue',
            'mid_of_q2_2024' => 'Second Quarter of 2024 ',
            //            'activating_the_marketplace_dashboard' => 'Activating the Marketplace Dashboard',
            'promotional_activities_for_real_estate_investors' => 'Continued to carry out promotional activities for RWA investors. Interviews with various media organizations were planned.',
            //            'announcement_campaign_for_supplying_products_to_terramirum' => 'Announcement Campaign for Supplying Products to Terramirum',
            //            'interviews_with_various_media_organizations' => 'Interviews with Various Media Organizations',
            'mid_of_q3_2024' => 'Third Quarter of 2024',
            //            'new_real_estate_based_models' => 'New Real Estate-based Models',
            'terra_mirum_chain_designed_and_services_put_into_service' => 'After the Launchpad revenues, TerraMirum\'s own chain will be designed and all services will be offered in this chain. Creation of new revenue models based on RWA\'s (rentals, tourism villas, timeshares). Start of the ICO Process (Seed, Private, Public). Creation of an announcement campaign to provide products and users to TerraMirum.',
            //            'creation_of_new_real_estate_based_income_models' => 'Creation of New Real Estate-based Income Models',
            'q4_2024'        => 'Last Quarter of 2024 ',
            'q4_2024_detail' => 'Creation of new collaborations. Commencement of the crowdfunding process. Secondary Market (Secondary Market) infrastructure works to be initiated.',
            'q1_2025'        => 'First Quarter of 2025',
            'q2_2025'        => 'Second Quarter of 2025',
            'q3_2025'        => 'Third Quarter of 2025',
            'q1_2025_detail' => 'Continuation of work on the Secondary Market. Start of work for Marketplace Activation of Marketplace dashboard supported with various data sets; Continued testing of Marketplace dashboard.',
            'q2_2025_detail' => 'Continuation of Secondary Market studies. Establishment of collaborations. Continuation of advertising campaigns.',
            'q3_2025_detail' => 'Secondary Market studies to be completed and put into operation.',
            'team_subtitle'  => 'Our Team',
            'team_title'     => 'The Leadership Team',
        ],
    ],

    'contact-us' => [
        'heading'     => 'Contact Us',
        'about-mirum' => [
            'title'       => 'About Mirum',
            'description' => 'The MIRUM token is classified as a utility token that can be used for real estate purchases and smart contracts. In order to secure the liquidity of the Credipto ecosystem, the supply is kept high, preventing excessive speculation and price instability. This step is necessary to ensure the smooth development of the ecosystem.',
        ],
        'contact-form' => [
            'title'     => 'Contact Us For Any Questions',
            'firstname' => 'First Name',
            'lastname'  => 'Last Name',
            'email'     => 'E-mail',
            'subject'   => 'Subject',
            'message'   => 'Message',
            'send'      => 'Send',
        ],
    ],

    'footer' => [
        'home'           => 'Home',
        'about'          => 'About Mirum',
        'faq'            => 'FAQ',
        'credipto'       => 'Credipto',
        'marketplace'    => 'Marketplace',
        'contact'        => 'Contact Us',
        'us'             => '',
        'footer-desc'    => 'The New Name of Investment in <br> Real World Assets with TerraMirum: <br>Mirum Token',
        'social-section' => [
            'title' => 'Social',
        ],
        'quick-links-section' => [
            'title' => 'Quick Links',
        ],
        'newsletter-section' => [
            'title' => 'Newsletter',
        ],
        'copy-right' => 'Copyright © :date. All Rights Reserved <a href=":url" target="_blank">:name</a>',
        'contracts'  => 'Contracts',
    ],

    'mirum-coin' => [
        'per-price'            => 'Per Price',
        'total-amount'         => 'Total Amount',
        'buy-now'              => 'Buy Now',
        'contract'             => 'Contract',
        'contract-description' => 'Contract Description',
        'contract-button'      => 'Show Contract',
        'sold-meter'           => 'Sold Meter',
        'target-raised'        => 'Target Raised',
        'overlay-title'        => 'Buy Mirum Token',
        'overlay-description'  => '<p class="text-white"> The proceeds from the token sale are intended to secure the financial requirements of the project by covering hardware and operational needs.</p>',
    ],
    'company_register' => [
        'companyMail-email'                 => 'Please enter a valid e-mail address.',
        'companyMail-required'              => '1 Please fill in the blank fields.',
        'companyMail-unique'                => 'The email address you entered is registered',
        'companyName-required'              => '2 Please fill in the blank fields.',
        'companyPhone-string'               => 'Please enter a valid Phone number.',
        'companyPhone-required'             => '3 Please fill in the blank fields.',
        'companyAddress-required'           => '4 Please fill in the blank fields.',
        'companyTaxNumber-string'           => 'Please enter a valid Tax number.',
        'companyTaxNumber-required'         => '5 Please fill in the blank fields.',
        'companyTaxNumber-min'              => 'The value you entered must be min: {min}.',
        'companyRepresentative-required'    => '6 Please fill in the blank fields.',
        'legalType-required'                => 'Please fill in the blank fields.',
        'toast-popup-warning-title'         => 'Warning!',
        'toast-popup-warning-message'       => 'Please Fill in the Blank Fields.',
        'toast-popup-warning-email-message' => 'Please enter a valid email address.',
    ],
];
